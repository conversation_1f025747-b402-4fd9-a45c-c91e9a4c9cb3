```diff
PS C:\Users\<USER>\Documents\MyData\MyProjects\Chatbot\chatbot> git show 822a1463
commit 822a1463e336510d3a07e616e7ab3e38fc69435b (HEAD -> 67-pr-21-implement-input-area-ui-e1s-e1s7)
Date:   Fri Aug 15 02:51:13 2025 +0200

    Add streaming support for chat messages
    
    - Introduce streaming capabilities for assistant responses, enabling real-time updates.
    - Update `ChatViewModel`, `MessageServiceImpl`, `ChatCompletionStrategy`, and related APIs for streaming flow handling.
    - Add SSE plugin to `Ktor` server configuration.
    - Extend request/response models to include a `stream` flag for streaming support.
    - Modify UI to handle streaming assistant message updates dynamically.
    - Enhance error handling and ensure proper session state updates during streaming.

diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
index 3e8f8c2..2f7f862 100644
--- a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt
@@ -54,7 +54,7 @@ fun appModule(baseUri: String): Module = module {
}

     // Provide ViewModels, injecting the required API clients
-    viewModel { ChatViewModel(get(), get(), get()) }
+    viewModel { ChatViewModel(get(), get(), get(), get()) }
     viewModel { SessionListViewModel(get(), get(), get()) }
     viewModel { ProviderConfigViewModel(get()) }
     viewModel { ModelConfigViewModel(get(), get()) }
     diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ChatApi.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ChatApi.kt
     index 31c0e01..d57e2b1 100644
     --- a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ChatApi.kt
     +++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ChatApi.kt
     @@ -3,8 +3,10 @@ package eu.torvian.chatbot.app.service.api
     import arrow.core.Either
     import eu.torvian.chatbot.common.api.ApiError
     import eu.torvian.chatbot.common.models.ChatMessage
     +import eu.torvian.chatbot.common.models.ChatUpdate
     import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
     import eu.torvian.chatbot.common.models.UpdateMessageRequest
     +import kotlinx.coroutines.flow.Flow

/**
* Frontend API interface for interacting with Chat message-related endpoints.
  @@ -18,9 +20,9 @@ interface ChatApi {

  /**
    * Sends a new user message to a specified session and processes the LLM response.
+     * This is for **non-streaming** responses.
      *
-     * Corresponds to `POST /api/v1/sessions/{sessionId}/messages`.
-     * (E1.S1, E1.S4, E1.S7)
+     * Corresponds to `POST /api/v1/sessions/{sessionId}/messages` with `stream=false`.
      *
      * @param sessionId The ID of the session to send the message to.
      * @param request The details of the new message, including content and optional parent ID.
@@ -29,6 +31,18 @@ interface ChatApi {
*/
suspend fun processNewMessage(sessionId: Long, request: ProcessNewMessageRequest): Either<ApiError, List<ChatMessage>>

+    /**
+     * Sends a new user message to a specified session and streams the LLM response back.
+     *
+     * Corresponds to `POST /api/v1/sessions/{sessionId}/messages` with `stream=true`.
+     *
+     * @param sessionId The ID of the session to send the message to.
+     * @param request The details of the new message.
+     * @return A [Flow] of [Either<ApiError, ChatUpdate>] representing the stream of updates.
+     *         The flow will emit various [ChatUpdate] types until [ChatUpdate.Done] or an error.
+     */
+    fun processNewMessageStreaming(sessionId: Long, request: ProcessNewMessageRequest): Flow<Either<ApiError, ChatUpdate>>
+
/**
* Updates the content of an existing message.
*
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorChatApiClient.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorChatApiClient.kt
index eb33b16..bcd3760 100644
--- a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorChatApiClient.kt
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorChatApiClient.kt
@@ -1,17 +1,28 @@
package eu.torvian.chatbot.app.service.api.ktor

import arrow.core.Either
+import arrow.core.left
+import arrow.core.right
import eu.torvian.chatbot.app.service.api.ChatApi
+import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.api.resources.MessageResource
import eu.torvian.chatbot.common.api.resources.SessionResource
import eu.torvian.chatbot.common.models.ChatMessage
+import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest
import io.ktor.client.*
import io.ktor.client.call.*
+import io.ktor.client.plugins.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
+import io.ktor.client.statement.*
+import io.ktor.http.*
+import io.ktor.utils.io.*
+import kotlinx.coroutines.flow.Flow
+import kotlinx.coroutines.flow.flow
+import kotlinx.serialization.json.Json

/**
* Ktor HttpClient implementation of the [ChatApi] interface.
  @@ -23,6 +34,18 @@ import io.ktor.client.request.*
* @property client The Ktor HttpClient instance injected for making requests.
  */
  class KtorChatApiClient(client: HttpClient) : BaseApiClient(client), ChatApi {
+    companion object {
+        private val logger = kmpLogger<KtorChatApiClient>()
+    }
+
+    // Need a Json instance for deserialization.
+    // Ensure this matches the configuration of the Ktor client's ContentNegotiation.
+    // It's best practice to inject this if your DI framework supports it.
+    private val json: Json = Json {
+        ignoreUnknownKeys = true
+        isLenient = true
+        encodeDefaults = true
+    }

     override suspend fun processNewMessage(
     sessionId: Long,
     @@ -34,10 +57,91 @@ class KtorChatApiClient(client: HttpClient) : BaseApiClient(client), ChatApi {
     client.post(SessionResource.ById.Messages(SessionResource.ById(SessionResource(), sessionId))) {
     // Set the request body with the ProcessNewMessageRequest DTO
     setBody(request)
+                timeout {
+                    requestTimeoutMillis = HttpTimeoutConfig.INFINITE_TIMEOUT_MS // Allow indefinite stream
+                }
             }.body<List<ChatMessage>>() // Expect a List<ChatMessage> in the response body on success (HTTP 201)
         }
  }

+    override fun processNewMessageStreaming(
+        sessionId: Long,
+        request: ProcessNewMessageRequest
+    ): Flow<Either<ApiError, ChatUpdate>> = flow {
+        try {
+            // Use preparePost and execute to get a streaming response
+            client.preparePost(SessionResource.ById.Messages(SessionResource.ById(SessionResource(), sessionId))) {
+                setBody(request)
+                // Explicitly request SSE content type if not already default
+                accept(ContentType.Text.EventStream)
+                timeout {
+                    requestTimeoutMillis = HttpTimeoutConfig.INFINITE_TIMEOUT_MS // Allow indefinite stream
+                }
+            }.execute { httpResponse ->
+                if (httpResponse.status.isSuccess()) {
+                    val channel: ByteReadChannel = httpResponse.bodyAsChannel()
+                    while (!channel.isClosedForRead) {
+                        val line = channel.readUTF8Line()
+                        if (line == null || line.isBlank()) continue // Skip empty lines
+                        if (line.startsWith("data:")) {
+                            val data = line.removePrefix("data:").trim()
+                            if (data.isNotBlank()) {
+                                try {
+                                    val chatUpdate = json.decodeFromString<ChatUpdate>(data)
+                                    emit(chatUpdate.right())
+                                    if (chatUpdate is ChatUpdate.Done || chatUpdate is ChatUpdate.ErrorUpdate) {
+                                        // Explicitly stop collecting if done or error event received
+                                        break
+                                    }
+                                } catch (e: Exception) {
+                                    logger.error("Failed to parse SSE data chunk for session $sessionId: '$data'", e)
+                                    emit(
+                                        ApiError(
+                                            statusCode = 500,
+                                            code = "parsing-error",
+                                            message = "Failed to parse chat update chunk",
+                                            details = mapOf("error" to (e.message ?: "Unknown parsing error"))
+                                        ).left()
+                                    )
+                                    break // Stop on parsing error
+                                }
+                            }
+                        } else if (line.startsWith("event:")) {
+                            // You can read the event type if needed, e.g., val eventType = line.removePrefix("event:").trim()
+                        }
+                        // Ignore other SSE fields like "id:", "retry:", etc.
+                    }
+                } else {
+                    // Handle non-2xx HTTP status codes immediately
+                    val errorBody = httpResponse.bodyAsText()
+                    val apiError = try {
+                        json.decodeFromString<ApiError>(errorBody)
+                    } catch (e: Exception) {
+                        logger.warn("Failed to parse error response body, creating generic error", e)
+                        ApiError(
+                            statusCode = httpResponse.status.value,
+                            code = "unknown-error",
+                            message = "HTTP ${httpResponse.status.value}: ${httpResponse.status.description}",
+                            details = mapOf("originalBody" to errorBody.take(200))
+                        )
+                    }
+                    logger.error("API call for session $sessionId failed with status ${httpResponse.status.value}: $apiError")
+                    emit(apiError.left())
+                }
+            }
+        } catch (e: Exception) {
+            logger.error("Network or streaming error during processNewMessageStreaming for session $sessionId", e)
+            emit(
+                ApiError(
+                    statusCode = 500,
+                    code = "network-error",
+                    message = "Network or communication error during streaming: ${e.message}",
+                    details = mapOf("error" to (e.message ?: "Unknown network error"))
+                ).left()
+            )
+        }
+    }
+
override suspend fun updateMessageContent(
messageId: Long,
request: UpdateMessageRequest
diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/createHttpClient.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/createHttpClient.kt
index 5c6e0df..82ed1cd 100644
--- a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/createHttpClient.kt
+++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/createHttpClient.kt
@@ -128,4 +128,10 @@ fun <T : HttpClientEngineConfig> HttpClientConfig<T>.configureClient(
logger = Logger.DEFAULT
level = logLevel
}
+
+    install(HttpTimeout) {
+        requestTimeoutMillis = 30_000
+        connectTimeoutMillis = 15_000
+        socketTimeoutMillis = 15_000
+    }
     }
     \ No newline at end of file
     diff --git a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
     index 4729569..430c9de 100644
     --- a/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
     +++ b/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt
     @@ -10,6 +10,7 @@ import eu.torvian.chatbot.app.generated.resources.error_loading_session
     import eu.torvian.chatbot.app.generated.resources.error_sending_message_short
     import eu.torvian.chatbot.app.service.api.ChatApi
     import eu.torvian.chatbot.app.service.api.SessionApi
     +import eu.torvian.chatbot.app.service.api.SettingsApi
     import eu.torvian.chatbot.app.service.misc.EventBus
     import eu.torvian.chatbot.app.utils.misc.kmpLogger
     import eu.torvian.chatbot.common.api.ApiError
     @@ -20,6 +21,10 @@ import kotlinx.coroutines.Dispatchers
     import kotlinx.coroutines.flow.*
     import kotlinx.coroutines.launch
     import kotlinx.datetime.Clock
     +import kotlinx.serialization.json.Json
     +import kotlinx.serialization.json.booleanOrNull
     +import kotlinx.serialization.json.jsonObject
     +import kotlinx.serialization.json.jsonPrimitive
     import org.jetbrains.compose.resources.getString

/**
@@ -37,6 +42,7 @@ import org.jetbrains.compose.resources.getString
* @constructor
* @param sessionApi The API client for session-related operations.
* @param chatApi The API client for chat message-related operations.
+ * @param settingsApi The API client for model settings.
* @param eventBus The event bus for emitting global events like retry-able errors.
* @param uiDispatcher The dispatcher to use for UI-related coroutines. Defaults to Main.
* @param clock The clock to use for timestamping. Defaults to System clock.
  @@ -52,6 +58,7 @@ import org.jetbrains.compose.resources.getString
  class ChatViewModel(
  private val sessionApi: SessionApi,
  private val chatApi: ChatApi,
+    private val settingsApi: SettingsApi,
     private val eventBus: EventBus,
     private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main,
     private val clock: Clock = Clock.System
     @@ -78,18 +85,39 @@ class ChatViewModel(
     */
     val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()

+    // New state to hold the actively streaming assistant message
+    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)
+
/**
* The list of messages to display in the UI, representing the currently selected thread branch.
-     * This is derived from the session's full list of messages and the current leaf message ID.
+     * This is derived from the session's full list of messages and the current leaf message ID,
+     * combined with any actively streaming message.
      */
  val displayedMessages: StateFlow<List<ChatMessage>> = combine(
  _sessionState.filterIsInstance<UiState.Success<ChatSession>>()
  .map { it.data.messages }, // Flow of just the message list when in Success state
-        _currentBranchLeafId // Flow of the current leaf ID
-    ) { allMessages, leafId ->
+        _currentBranchLeafId, // Flow of the current leaf ID
+        _streamingAssistantMessage // Include the streaming message flow
+    ) { allPersistedMessages, leafId, streamingAssistantMessage ->
     // This is where the UI state logic (E1.S5) happens:
-        // Build the thread branch whenever the message list or the leaf ID changes.
-        buildThreadBranch(allMessages, leafId)
+        // Prepare the list of messages for `buildThreadBranch`.
+        val messagesForBranching = if (streamingAssistantMessage != null) {
+            // If streaming, create a copy of the persisted messages
+            // and replace/add the streaming message for real-time display.
+            // This ensures `buildThreadBranch` always sees the latest state.
+            val updatedMessages = allPersistedMessages.toMutableList()
+            val existingIndex = updatedMessages.indexOfFirst { it.id == streamingAssistantMessage.id }
+            if (existingIndex != -1) {
+                updatedMessages[existingIndex] = streamingAssistantMessage
+            } else {
+                updatedMessages.add(streamingAssistantMessage)
+            }
+            updatedMessages.toList()
+        } else {
+            allPersistedMessages
+        }
+        logger.info("Building thread branch for leaf ID: $leafId with ${messagesForBranching.size} messages.")
+        buildThreadBranch(messagesForBranching, leafId)
  }
  .stateIn(
  scope = CoroutineScope(viewModelScope.coroutineContext + uiDispatcher),
  @@ -225,6 +253,7 @@ class ChatViewModel(
  _replyTargetMessage.value = null
  _editingMessage.value = null
  _currentBranchLeafId.value = null
+        _streamingAssistantMessage.value = null
  }

  /**
  @@ -252,29 +281,155 @@ class ChatViewModel(
  _isSendingMessage.value = true // Set sending state to true (E1.S3)

             try {
-                chatApi.processNewMessage(
-                    sessionId = currentSession.id,
-                    request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
-                )
-                    .fold(
-                        ifLeft = { error ->
-                            logger.error("Send message API error: ${error.code} - ${error.message}")
-                            // Emit to EventBus for Snackbar display (E1.S6)
-                            eventBus.emitEvent(
-                                apiRequestError(
-                                    apiError = error,
-                                    shortMessage = getString(Res.string.error_sending_message_short),
+                // Check if streaming is enabled in settings
+                val isStreamingEnabled = checkStreamingEnabled(currentSession.currentSettingsId)
+
+                if (isStreamingEnabled) {
+                    // Handle streaming message
+                    handleStreamingMessage(currentSession, content, parentId)
+                } else {
+                    // Handle non-streaming message (existing logic)
+                    chatApi.processNewMessage(
+                        sessionId = currentSession.id,
+                        request = ProcessNewMessageRequest(content = content, parentMessageId = parentId, stream = false)
+                    )
+                        .fold(
+                            ifLeft = { error ->
+                                logger.error("Send message API error: ${error.code} - ${error.message}")
+                                // Emit to EventBus for Snackbar display (E1.S6)
+                                eventBus.emitEvent(
+                                    apiRequestError(
+                                        apiError = error,
+                                        shortMessage = getString(Res.string.error_sending_message_short),
+                                    )
                                 )
+                            },
+                            ifRight = { newMessages ->
+                                // Handle Success case (E1.S4)
+                                // We received the new user and assistant messages.
+                                // Add them to the messages list in the current session state Flow.
+                                val updatedMessages = currentSession.messages + newMessages
+                                val newLeafId = newMessages.lastOrNull()?.id // Assume assistant message is the new leaf
+
+                                // Update the session object inside the Success state with the new messages
+                                _sessionState.value = UiState.Success(
+                                    currentSession.copy(
+                                        messages = updatedMessages,
+                                        currentLeafMessageId = newLeafId,
+                                        updatedAt = clock.now()
+                                    )
+                                )
+                                _currentBranchLeafId.value = newLeafId // Update the separate leaf state Flow
+
+                                // Reset reply target (E1.S7)
+                                _replyTargetMessage.value = null
+                                _inputContent.value = "" // Clear input field
+                            }
+                        )
+                }
+            } finally {
+                _isSendingMessage.value = false // Always reset sending state
+            }
+        }
+    }
+
+    /**
+     * Checks if streaming is enabled in the current model settings.
+     */
+    private suspend fun checkStreamingEnabled(settingsId: Long?): Boolean {
+        if (settingsId == null) return false
+
+        return settingsApi.getSettingsById(settingsId).fold(
+            ifLeft = { error ->
+                logger.warn("Failed to fetch settings for streaming check: ${error.message}")
+                false // Default to non-streaming if settings can't be fetched
+            },
+            ifRight = { settings ->
+                // Check if streaming is enabled in the settings
+                // This assumes the settings object has a streaming property
+                try {
+                    val settingsJson = Json.parseToJsonElement(Json.encodeToString(settings)).jsonObject
+                    settingsJson["streaming"]?.jsonPrimitive?.booleanOrNull ?: true
+                } catch (e: Exception) {
+                    logger.warn("Failed to parse streaming setting from settings: ${e.message}")
+                    false
+                }
+            }
+        )
+    }
+
+    /**
+     * Handles streaming message processing.
+     */
+    private suspend fun handleStreamingMessage(currentSession: ChatSession, content: String, parentId: Long?) {
+        var userMessage: ChatMessage.UserMessage? = null
+        chatApi.processNewMessageStreaming(
+            sessionId = currentSession.id,
+            request = ProcessNewMessageRequest(content = content, parentMessageId = parentId, stream = true)
+        ).collect { eitherUpdate ->
+            eitherUpdate.fold(
+                ifLeft = { error ->
+                    logger.error("Streaming message API error: ${error.code} - ${error.message}")
+                    // Clear any streaming state and emit error
+                    _streamingAssistantMessage.value = null
+                    eventBus.emitEvent(
+                        apiRequestError(
+                            apiError = error,
+                            shortMessage = getString(Res.string.error_sending_message_short),
+                        )
+                    )
+                },
+                ifRight = { chatUpdate ->
+                    when (chatUpdate) {
+                        is ChatUpdate.UserMessageUpdate -> {
+                            logger.info("User message update: ${chatUpdate.message.content}")
+                            // Add the user message to the session immediately
+                            userMessage = chatUpdate.message
+                            val updatedMessages = currentSession.messages + chatUpdate.message
+                            _sessionState.value = UiState.Success(
+                                currentSession.copy(
+                                    messages = updatedMessages,
+                                    updatedAt = clock.now()
+                                )
+                            )
+                            _currentBranchLeafId.value = chatUpdate.message.id
+                            // Clear input and reply target after user message is confirmed
+                            _inputContent.value = ""
+                            _replyTargetMessage.value = null
+                        }
+                        is ChatUpdate.AssistantMessageStart -> {
+                            logger.info("Assistant message start: ${chatUpdate.messageId}")
+                            // Create a new streaming assistant message with empty content
+                            val streamingMessage = ChatMessage.AssistantMessage(
+                                id = chatUpdate.messageId,
+                                sessionId = chatUpdate.sessionId,
+                                content = "",
+                                parentMessageId = chatUpdate.parentId,
+                                childrenMessageIds = emptyList(),
+                                createdAt = chatUpdate.createdAt,
+                                updatedAt = chatUpdate.createdAt,
+                                modelId = chatUpdate.modelId,
+                                settingsId = chatUpdate.settingsId
                             )
-                        },
-                        ifRight = { newMessages ->
-                            // Handle Success case (E1.S4)
-                            // We received the new user and assistant messages.
-                            // Add them to the messages list in the current session state Flow.
-                            val updatedMessages = currentSession.messages + newMessages
-                            val newLeafId = newMessages.lastOrNull()?.id // Assume assistant message is the new leaf
-
-                            // Update the session object inside the Success state with the new messages
+                            _currentBranchLeafId.value = chatUpdate.messageId
+                            _streamingAssistantMessage.value = streamingMessage
+                        }
+                        is ChatUpdate.AssistantMessageDelta -> {
+                            logger.info("Assistant message delta: ${chatUpdate.deltaContent}")
+                            // Update the streaming message content
+                            _streamingAssistantMessage.value?.let { currentStreamingMessage ->
+                                _streamingAssistantMessage.value = currentStreamingMessage.copy(
+                                    content = currentStreamingMessage.content + chatUpdate.deltaContent
+                                )
+                            }
+                        }
+                        is ChatUpdate.AssistantMessageEnd -> {
+                            logger.info("Assistant message end: ${chatUpdate.finalMessage.content}")
+                            // Replace the temporary streaming message with the final persisted message
+                            val finalMessage = chatUpdate.finalMessage
+                            val updatedMessages = currentSession.messages + userMessage!! + finalMessage
+                            val newLeafId = finalMessage.id
+
                           _sessionState.value = UiState.Success(
                               currentSession.copy(
                                   messages = updatedMessages,
@@ -282,16 +437,27 @@ class ChatViewModel(
updatedAt = clock.now()
)
)
-                            _currentBranchLeafId.value = newLeafId // Update the separate leaf state Flow
-
-                            // Reset reply target (E1.S7)
-                            _replyTargetMessage.value = null
-                            _inputContent.value = "" // Clear input field
+                            _currentBranchLeafId.value = newLeafId
+                            _streamingAssistantMessage.value = null // Clear streaming state
                         }
-                    )
-            } finally {
-                _isSendingMessage.value = false // Always reset sending state
-            }
+                        is ChatUpdate.ErrorUpdate -> {
+                            // Handle error during streaming
+                            logger.error("Streaming error: ${chatUpdate.error.message}")
+                            _streamingAssistantMessage.value = null
+                            eventBus.emitEvent(
+                                apiRequestError(
+                                    apiError = chatUpdate.error,
+                                    shortMessage = getString(Res.string.error_sending_message_short),
+                                )
+                            )
+                        }
+                        ChatUpdate.Done -> {
+                            // Streaming completed successfully
+                            logger.info("Streaming completed for session ${currentSession.id}")
+                        }
+                    }
+                }
+            )
         }
  }

diff --git a/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ChatUpdate.kt b/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ChatUpdate.kt
new file mode 100644
index 0000000..05f8a7e
--- /dev/null
+++ b/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ChatUpdate.kt
@@ -0,0 +1,63 @@
+package eu.torvian.chatbot.common.models
+
+import kotlinx.datetime.Instant
+import kotlinx.serialization.Serializable
+import eu.torvian.chatbot.common.api.ApiError
+
+/**
+ * Represents a discrete update event sent during a streaming chat completion.
+ * Used for server-to-client communication to incrementally build the chat message.
+ */
  +@Serializable
  +sealed class ChatUpdate {
+    /**
+     * Sent once the user's message has been processed and saved on the server.
+     */
+    @Serializable
+    data class UserMessageUpdate(val message: ChatMessage.UserMessage) : ChatUpdate()
+
+    /**
+     * Sent when the assistant's message starts, providing initial metadata and a temporary ID.
+     * The content will be empty or a single token.
+     * The `messageId` here is a client-side temporary ID.
+     */
+    @Serializable
+    data class AssistantMessageStart(
+        val messageId: Long, // Client-side temporary ID
+        val parentId: Long,
+        val sessionId: Long,
+        val createdAt: Instant,
+        val modelId: Long?,
+        val settingsId: Long?
+    ) : ChatUpdate()
+
+    /**
+     * Sent for each new content chunk from the LLM.
+     * The client should append `deltaContent` to the existing message identified by `messageId`.
+     * The `messageId` here is the client-side temporary ID.
+     */
+    @Serializable
+    data class AssistantMessageDelta(val messageId: Long, val deltaContent: String) : ChatUpdate()
+
+    /**
+     * Sent when the assistant's message generation is complete.
+     * Provides the temporary ID that was used and the final, persisted message with its real ID.
+     */
+    @Serializable
+    data class AssistantMessageEnd(
+        val tempMessageId: Long, // The temporary ID used during streaming
+        val finalMessage: ChatMessage.AssistantMessage // The final, persisted message with its real ID
+    ) : ChatUpdate()
+
+    /**
+     * Sent if an error occurs during the streaming process.
+     */
+    @Serializable
+    data class ErrorUpdate(val error: ApiError) : ChatUpdate()
+
+    /**
+     * Sent as the final signal to indicate the end of the entire stream.
+     */
+    @Serializable
+    data object Done : ChatUpdate()
     +}
     diff --git a/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ProcessNewMessageRequest.kt b/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ProcessNewMessageRequest.kt
     index b560a0d..8952dec 100644
     --- a/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ProcessNewMessageRequest.kt
     +++ b/common/src/commonMain/kotlin/eu/torvian/chatbot/common/models/ProcessNewMessageRequest.kt
     @@ -7,9 +7,11 @@ import kotlinx.serialization.Serializable
*
* @property content The user's message content.
* @property parentMessageId The ID of the message this is a reply to (null for initial messages or if replying to the root of a new thread branch).
+ * @property stream Whether the client requests a streaming response. Defaults to true.
    */
    @Serializable
    data class ProcessNewMessageRequest(
    val content: String,
-    val parentMessageId: Long? = null
+    val parentMessageId: Long? = null,
+    val stream: Boolean = true
     )
     \ No newline at end of file
     diff --git a/gradle/libs.versions.toml b/gradle/libs.versions.toml
     index 7f9ec1f..251af6f 100644
     --- a/gradle/libs.versions.toml
     +++ b/gradle/libs.versions.toml
     @@ -56,6 +56,7 @@ ktor-server-content-negotiation = { module = "io.ktor:ktor-server-content-negoti
     ktor-server-call-logging = { module = "io.ktor:ktor-server-call-logging", version.ref = "ktor" }
     ktor-server-double-receive = { module = "io.ktor:ktor-server-double-receive", version.ref = "ktor" }
     ktor-server-cors = { module = "io.ktor:ktor-server-cors", version.ref = "ktor" }
     +ktor-server-sse = { module = "io.ktor:ktor-server-sse", version.ref = "ktor" }

# Ktor Client
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
@@ -142,7 +143,8 @@ ktor-server = [
"ktor-server-call-logging",
"ktor-server-double-receive",
"ktor-server-resources",
-    "ktor-server-cors"
+    "ktor-server-cors",
+    "ktor-server-sse"
     ]

ktor-server-test = [
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/MessageDao.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/MessageDao.kt
index ad68e2e..7b5fd85 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/MessageDao.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/MessageDao.kt
@@ -34,13 +34,13 @@ interface MessageDao {
* @param sessionId The ID of the session the message belongs to.
* @param content The text content of the message.
* @param parentMessageId Optional ID of the parent message (null for root messages).
-     * @return Either a [InsertMessageError] or the newly created [ChatMessage] object.
+     * @return Either a [InsertMessageError] or the newly created [ChatMessage.UserMessage].
      */
  suspend fun insertUserMessage(
  sessionId: Long,
  content: String,
  parentMessageId: Long?
-    ): Either<InsertMessageError, ChatMessage>
+    ): Either<InsertMessageError, ChatMessage.UserMessage>

     /**
    * Inserts a new assistant message record into the database.
      @@ -51,7 +51,7 @@ interface MessageDao {
    * @param parentMessageId Optional ID of the parent message (null for root messages).
    * @param modelId Optional ID of the model used (for assistant messages).
    * @param settingsId Optional ID of the settings profile used (for assistant messages).
-     * @return Either a [InsertMessageError] or the newly created [ChatMessage] object.
+     * @return Either a [InsertMessageError] or the newly created [ChatMessage.AssistantMessage].
      */
  suspend fun insertAssistantMessage(
  sessionId: Long,
  @@ -59,7 +59,7 @@ interface MessageDao {
  parentMessageId: Long?,
  modelId: Long?,
  settingsId: Long?
-    ): Either<InsertMessageError, ChatMessage>
+    ): Either<InsertMessageError, ChatMessage.AssistantMessage>

     /**
    * Updates the content and updated timestamp of an existing message.
      diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/exposed/MessageDaoExposed.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/exposed/MessageDaoExposed.kt
      index 6548a11..23c9de3 100644
      --- a/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/exposed/MessageDaoExposed.kt
      +++ b/server/src/main/kotlin/eu/torvian/chatbot/server/data/dao/exposed/MessageDaoExposed.kt
      @@ -76,7 +76,7 @@ class MessageDaoExposed(
      sessionId: Long,
      content: String,
      parentMessageId: Long?
-    ): Either<InsertMessageError, ChatMessage> =
+    ): Either<InsertMessageError, ChatMessage.UserMessage> =
     transactionScope.transaction {
     either {
     if (parentMessageId != null) {
     @@ -111,7 +111,7 @@ class MessageDaoExposed(
     parentMessageId: Long?,
     modelId: Long?,
     settingsId: Long?
-    ): Either<InsertMessageError, ChatMessage> =
+    ): Either<InsertMessageError, ChatMessage.AssistantMessage> =
     transactionScope.transaction {
     either {
     if (parentMessageId != null) {
     diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/configureKtor.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/configureKtor.kt
     index c81f86d..1169aa1 100644
     --- a/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/configureKtor.kt
     +++ b/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/configureKtor.kt
     @@ -5,6 +5,7 @@ import io.ktor.server.application.Application
     import io.ktor.server.application.install
     import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
     import io.ktor.server.resources.Resources
     +import io.ktor.server.sse.SSE
     import kotlinx.serialization.json.Json

/**
@@ -18,4 +19,7 @@ fun Application.configureKtor() {

     // Install the Resources plugin for type-safe routing
     install(Resources)
+
+    // Install the SSE plugin for server-sent events support
+    install(SSE)
     }
     \ No newline at end of file
     diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/routes/configureSessionRoutes.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/routes/configureSessionRoutes.kt
     index 599f41b..c8d551f 100644
     --- a/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/routes/configureSessionRoutes.kt
     +++ b/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/routes/configureSessionRoutes.kt
     @@ -14,6 +14,10 @@ import io.ktor.server.request.*
     import io.ktor.server.resources.*
     import io.ktor.server.response.*
     import io.ktor.server.routing.Route
     +import io.ktor.server.sse.*
     +import kotlinx.serialization.encodeToString
     +import kotlinx.serialization.json.Json
     +import org.koin.ktor.ext.inject

/**
* Configures routes related to Sessions (/api/v1/sessions) using Ktor Resources.
  @@ -22,6 +26,7 @@ fun Route.configureSessionRoutes(
  sessionService: SessionService,
  messageService: MessageService
  ) {
+    val json: Json by inject() // Inject the Json instance for serialization
     // GET /api/v1/sessions - List all sessions
     get<SessionResource> {
     call.respond(sessionService.getAllSessionsSummaries())
     @@ -185,38 +190,82 @@ fun Route.configureSessionRoutes(
     post<SessionResource.ById.Messages> { resource ->
     val sessionId = resource.parent.sessionId
     val request = call.receive<ProcessNewMessageRequest>()
-        call.respondEither(
-            messageService.processNewMessage(
-                sessionId,
-                request.content,
-                request.parentMessageId
-            ), HttpStatusCode.Created
-        ) { error ->
-            when (error) {
-                is ProcessNewMessageError.SessionNotFound ->
-                    apiError(
-                        CommonApiErrorCodes.NOT_FOUND,
-                        "Session not found",
-                        "sessionId" to error.sessionId.toString()
-                    )
-
-                is ProcessNewMessageError.ParentNotInSession ->
-                    apiError(
-                        CommonApiErrorCodes.INVALID_STATE,
-                        "Parent message does not belong to this session",
-                        "sessionId" to error.sessionId.toString(),
-                        "parentId" to error.parentId.toString()
-                    )

-                is ProcessNewMessageError.ModelConfigurationError ->
-                    apiError(
-                        ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR,
-                        "LLM configuration error",
-                        "details" to error.message
+        if (request.stream) {
+            // Handle Streaming Request using SSE
+            call.respondTextWriter(contentType = ContentType.Text.EventStream) {
+                messageService.processNewMessageStreaming(
+                    sessionId,
+                    request.content,
+                    request.parentMessageId
+                ).collect { eitherUpdate ->
+                    eitherUpdate.fold(
+                        ifLeft = { error ->
+                            val apiError = when (error) {
+                                is ProcessNewMessageError.SessionNotFound ->
+                                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.sessionId.toString())
+                                is ProcessNewMessageError.ParentNotInSession ->
+                                    apiError(CommonApiErrorCodes.INVALID_STATE, "Parent message does not belong to this session", "sessionId" to error.sessionId.toString(), "parentId" to error.parentId.toString())        
+                                is ProcessNewMessageError.ModelConfigurationError ->
+                                    apiError(ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR, "LLM configuration error", "details" to error.message)
+                                is ProcessNewMessageError.ExternalServiceError ->
+                                    apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
+                            }
+                            // Send an error event to the client
+                            write("event: error\n")
+                            write("data: ${json.encodeToString(ChatUpdate.ErrorUpdate(apiError))}\n\n")
+                            flush()
+                        },
+                        ifRight = { chatUpdate ->
+                            // Determine the SSE event type based on ChatUpdate sealed class member
+                            val eventType = when (chatUpdate) {
+                                is ChatUpdate.UserMessageUpdate -> "user_message"
+                                is ChatUpdate.AssistantMessageStart -> "assistant_message_start"
+                                is ChatUpdate.AssistantMessageDelta -> "assistant_message_delta"
+                                is ChatUpdate.AssistantMessageEnd -> "assistant_message_end"
+                                is ChatUpdate.ErrorUpdate -> "error" // Redundant if left branch handles, but safe
+                                ChatUpdate.Done -> "done"
+                            }
+                            // Send the ChatUpdate object serialized as JSON
+                            write("event: $eventType\n")
+                            write("data: ${json.encodeToString(chatUpdate)}\n\n")
+                            flush()
+                        }
                     )
-
-                is ProcessNewMessageError.ExternalServiceError ->
-                    apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
+                }
+            }
+        } else {
+            // Handle Non-Streaming Request
+            call.respondEither(
+                messageService.processNewMessage(
+                    sessionId,
+                    request.content,
+                    request.parentMessageId
+                ), HttpStatusCode.Created
+            ) { error ->
+                when (error) {
+                    is ProcessNewMessageError.SessionNotFound ->
+                        apiError(
+                            CommonApiErrorCodes.NOT_FOUND,
+                            "Session not found",
+                            "sessionId" to error.sessionId.toString()
+                        )
+                    is ProcessNewMessageError.ParentNotInSession ->
+                        apiError(
+                            CommonApiErrorCodes.INVALID_STATE,
+                            "Parent message does not belong to this session",
+                            "sessionId" to error.sessionId.toString(),
+                            "parentId" to error.parentId.toString()
+                        )
+                    is ProcessNewMessageError.ModelConfigurationError ->
+                        apiError(
+                            ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR,
+                            "LLM configuration error",
+                            "details" to error.message
+                        )
+                    is ProcessNewMessageError.ExternalServiceError ->
+                        apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
+                }
             }
         }
  }
  diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/main/mainModule.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/main/mainModule.kt
  index 4de009c..3829dfe 100644
  --- a/server/src/main/kotlin/eu/torvian/chatbot/server/main/mainModule.kt
  +++ b/server/src/main/kotlin/eu/torvian/chatbot/server/main/mainModule.kt
  @@ -8,6 +8,7 @@ import eu.torvian.chatbot.server.service.llm.strategy.OpenAIChatStrategy
  import eu.torvian.chatbot.server.service.llm.strategy.OllamaChatStrategy
  import io.ktor.client.*
  import io.ktor.client.engine.cio.*
  +import io.ktor.client.plugins.HttpTimeout
  import io.ktor.client.plugins.contentnegotiation.*
  import io.ktor.serialization.kotlinx.json.*
  import io.ktor.server.application.*
  @@ -40,6 +41,7 @@ fun mainModule(application: Application) = module {
  install(ContentNegotiation) {
  json(get<Json>())
  }
+            install(HttpTimeout)
         }
  }

diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/MessageService.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/MessageService.kt
index 6c7bcfb..aa1d908 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/MessageService.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/MessageService.kt
@@ -2,9 +2,11 @@ package eu.torvian.chatbot.server.service.core

import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
+import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.service.core.error.message.DeleteMessageError
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.message.UpdateMessageContentError
+import kotlinx.coroutines.flow.Flow

/**
* Service interface for managing Chat Messages and their threading relationships.
  @@ -19,16 +21,13 @@ interface MessageService {
  suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage>

  /**
-     * Processes a new incoming user message (including replies).
-     *
-     * Orchestrates saving the user message, building LLM context (thread-aware),
-     * calling the LLM, saving the assistant message, and
-     * updating thread relationships in the database.
+     * Processes a new incoming user message and awaits the full LLM response.
+     * The assistant message is saved only after the full response is received.
      *
      * @param sessionId The ID of the session the message belongs to.
      * @param content The user's message content.
      * @param parentMessageId Optional ID of the message being replied to (null for root messages).
-     * @return Either a [ProcessNewMessageError] if processing fails (e.g., session/parent not found, LLM config error, LLM API error),
+     * @return Either a [ProcessNewMessageError] if processing fails,
      *         or a list containing the newly created user and assistant messages ([userMsg, assistantMsg]).
      */
  suspend fun processNewMessage(
  @@ -37,6 +36,23 @@ interface MessageService {
  parentMessageId: Long? = null
  ): Either<ProcessNewMessageError, List<ChatMessage>>

+    /**
+     * Processes a new incoming user message and streams the LLM response.
+     * The user message is saved immediately. The assistant message is saved only upon successful completion of the stream.
+     *
+     * @param sessionId The ID of the session the message belongs to.
+     * @param content The user's message content.
+     * @param parentMessageId Optional ID of the message being replied to.
+     * @return A Flow of Either<ProcessNewMessageError, ChatUpdate>.
+     *         The flow emits `ChatUpdate` events (user message, assistant start, deltas, end)
+     *         or `ProcessNewMessageError` if an error occurs during streaming.
+     */
+    fun processNewMessageStreaming(
+        sessionId: Long,
+        content: String,
+        parentMessageId: Long? = null
+    ): Flow<Either<ProcessNewMessageError, ChatUpdate>>
+
/**
* Updates the content of an existing message.
* @param id The ID of the message to update.
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/error/message/ProcessNewMessageError.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/error/message/ProcessNewMessageError.kt    
index 0bd8456..4753bab 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/error/message/ProcessNewMessageError.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/error/message/ProcessNewMessageError.kt
@@ -1,5 +1,9 @@
package eu.torvian.chatbot.server.service.core.error.message

+import eu.torvian.chatbot.common.api.ApiError
+import eu.torvian.chatbot.common.api.ChatbotApiErrorCodes
+import eu.torvian.chatbot.common.api.CommonApiErrorCodes
+import eu.torvian.chatbot.common.api.apiError
import eu.torvian.chatbot.server.service.llm.LLMCompletionError

/**
@@ -39,3 +43,17 @@ sealed interface ProcessNewMessageError {
*/
data class ExternalServiceError(val llmError: LLMCompletionError) : ProcessNewMessageError // Holds the specific LLM error
}
+
+/**
+ * Extension function to convert ProcessNewMessageError to ApiError for HTTP responses.
+ */
  +fun ProcessNewMessageError.toApiError(): ApiError = when (this) {
+    is ProcessNewMessageError.SessionNotFound ->
+        apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to sessionId.toString())
+    is ProcessNewMessageError.ParentNotInSession ->
+        apiError(CommonApiErrorCodes.INVALID_STATE, "Parent message does not belong to this session", "sessionId" to sessionId.toString(), "parentId" to parentId.toString())
+    is ProcessNewMessageError.ModelConfigurationError ->
+        apiError(ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR, "LLM configuration error", "details" to message)
+    is ProcessNewMessageError.ExternalServiceError ->
+        apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to llmError.toString())
+}
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/impl/MessageServiceImpl.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/impl/MessageServiceImpl.kt
index 2622752..46d9f94 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/impl/MessageServiceImpl.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/impl/MessageServiceImpl.kt
@@ -1,27 +1,54 @@
package eu.torvian.chatbot.server.service.core.impl

-import arrow.core.*
-import arrow.core.raise.*
+import arrow.core.Either
+import arrow.core.getOrElse
+import arrow.core.left
+import arrow.core.raise.either
+import arrow.core.raise.withError
+import arrow.core.right
import eu.torvian.chatbot.common.models.ChatMessage
+import eu.torvian.chatbot.common.models.ChatSession
+import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
-import eu.torvian.chatbot.server.data.dao.error.*
-import eu.torvian.chatbot.server.service.llm.LLMApiClient
-import eu.torvian.chatbot.server.service.llm.LLMCompletionError
+import eu.torvian.chatbot.server.data.dao.error.InsertMessageError
+import eu.torvian.chatbot.server.data.dao.error.MessageAddChildError
+import eu.torvian.chatbot.server.data.dao.error.MessageError
+import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.service.core.LLMModelService
import eu.torvian.chatbot.server.service.core.LLMProviderService
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.ModelSettingsService
-import eu.torvian.chatbot.server.service.core.error.message.*
-import eu.torvian.chatbot.server.service.core.error.model.*
-import eu.torvian.chatbot.server.service.core.error.provider.*
-import eu.torvian.chatbot.server.service.core.error.settings.*
+import eu.torvian.chatbot.server.service.core.error.message.DeleteMessageError
+import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
+import eu.torvian.chatbot.server.service.core.error.message.UpdateMessageContentError
+import eu.torvian.chatbot.server.service.core.error.message.toApiError
+import eu.torvian.chatbot.server.service.core.error.model.GetModelError
+import eu.torvian.chatbot.server.service.core.error.provider.GetProviderError
+import eu.torvian.chatbot.server.service.core.error.settings.GetSettingsByIdError
+import eu.torvian.chatbot.server.service.llm.LLMApiClient
+import eu.torvian.chatbot.server.service.llm.LLMCompletionError
+import eu.torvian.chatbot.server.service.llm.LLMCompletionResult
+import eu.torvian.chatbot.server.service.llm.LLMStreamChunk
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
+import kotlinx.coroutines.flow.Flow
+import kotlinx.coroutines.flow.channelFlow
+import kotlinx.datetime.Clock
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

+/**
+ * Data class to hold LLM configuration components.
+ */
  +private data class LlmConfig(
+    val model: eu.torvian.chatbot.common.models.LLMModel,
+    val settings: eu.torvian.chatbot.common.models.ModelSettings,
+    val provider: eu.torvian.chatbot.common.models.LLMProvider,
+    val apiKey: String?
     +)
+
/**
* Implementation of the [MessageService] interface.
* Orchestrates message persistence, threading, and LLM interaction.
  @@ -68,6 +95,7 @@ class MessageServiceImpl(
  is InsertMessageError.SessionNotFound -> {
  throw IllegalStateException("Session $sessionId not found after validation")
  }
+
                       is InsertMessageError.ParentNotInSession -> {
                           ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                       }
@@ -188,6 +216,238 @@ class MessageServiceImpl(
}
}

+    override fun processNewMessageStreaming(
+        sessionId: Long,
+        content: String,
+        parentMessageId: Long?
+    ): Flow<Either<ProcessNewMessageError, ChatUpdate>> = channelFlow {
+        // 1. Save user message immediately in its own transaction block.
+        // This ensures the user message is persisted even if streaming fails later.
+        val userMessage = transactionScope.transaction {
+            either {
+                val session = sessionDao.getSessionById(sessionId)
+                    .getOrElse { raise(ProcessNewMessageError.SessionNotFound(sessionId)) }
+                val userMsg = messageDao.insertUserMessage(sessionId, content, parentMessageId).mapLeft { daoError ->
+                    when (daoError) {
+                        is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(
+                            daoError.sessionId,
+                            daoError.parentId
+                        )
+
+                        is InsertMessageError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.sessionId)
+                    }
+                }.bind()
+                if (parentMessageId != null) {
+                    messageDao.addChildToMessage(parentMessageId, userMsg.id).mapLeft { linkError ->
+                        // Log a warning, but don't fail the whole operation for a linking issue
+                        logger.warn("Failed to link user message ${userMsg.id} as child to parent ${parentMessageId}: ${linkError::class.simpleName}")
+                        ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link user message.")
+                    }.bind()
+                }
+                userMsg
+            }
+        }.fold(
+            ifLeft = { error ->
+                logger.error("Initial user message save failed for session $sessionId: $error")
+                send(error.left()) // Send error to client and terminate flow
+                close()
+                return@channelFlow
+            },
+            ifRight = { it }
+        )
+
+        // 2. Emit the successfully saved user message as the first update
+        send(ChatUpdate.UserMessageUpdate(userMessage).right())
+
+        // 3. Get LLM model, settings, provider, and API key
+        val llmConfigResult = transactionScope.transaction {
+            val session = sessionDao.getSessionById(sessionId).getOrElse {
+                return@transaction ProcessNewMessageError.SessionNotFound(sessionId).left()
+            }
+            getLlmConfig(session, session.currentModelId ?: -1, session.currentSettingsId ?: -1)
+        }.getOrElse { error ->
+            logger.error("LLM configuration retrieval failed for session $sessionId: $error")
+            send(error.left()) // Send error to client and terminate flow
+            close()
+            return@channelFlow
+        }
+        val model = llmConfigResult.model
+        val settings = llmConfigResult.settings
+        val provider = llmConfigResult.provider
+        val apiKey = llmConfigResult.apiKey
+
+        // 4. Build context for LLM call (includes the newly saved user message)
+        val allMessagesInSession = transactionScope.transaction { messageDao.getMessagesBySessionId(sessionId) }
+        val context = buildContext(userMessage, allMessagesInSession)
+
+        // Generate a temporary ID for the assistant message on the server-side,
+        // which will be passed to the client to track the streaming message.
+        // The client will then replace this temporary ID with the real DB ID at the end.
+        val temporaryAssistantMessageId = -1L // A common convention for "not-yet-persisted" or client-only ID
+
+        // 5. Emit AssistantMessageStart with temporary ID to client
+        send(
+            ChatUpdate.AssistantMessageStart(
+                messageId = temporaryAssistantMessageId,
+                parentId = userMessage.id,
+                sessionId = sessionId,
+                createdAt = Clock.System.now(), // Client can use this for display
+                modelId = model.id,
+                settingsId = settings.id
+            ).right()
+        )
+
+        var accumulatedContent = ""
+        var finalUsage: LLMCompletionResult.UsageStats = LLMCompletionResult.UsageStats(0, 0, 0)
+        var finalFinishReason: String? = null
+
+        try {
+            // 6. Call LLM API in streaming mode and collect chunks
+            llmApiClient.completeChatStreaming(context, model, provider, settings, apiKey)
+                .collect { llmStreamChunkEither ->
+                    llmStreamChunkEither.fold(
+                        ifLeft = { llmError ->
+                            logger.error("LLM API streaming error for session $sessionId, provider ${provider.name}: $llmError")
+                            // If an error occurs in the stream, emit it and signal termination
+                            send(
+                                ChatUpdate.ErrorUpdate(
+                                    ProcessNewMessageError.ExternalServiceError(llmError).toApiError()
+                                ).right()
+                            )
+                            throw RuntimeException("LLM API streaming error: $llmError") // Propagate error to cause catch block
+                        },
+                        ifRight = { chunk ->
+                            when (chunk) {
+                                is LLMStreamChunk.ContentChunk -> {
+                                    accumulatedContent += chunk.deltaContent
+                                    finalFinishReason = chunk.finishReason
+                                        ?: finalFinishReason // Update finish reason if provided mid-stream
+                                    send(
+                                        ChatUpdate.AssistantMessageDelta(
+                                            temporaryAssistantMessageId,
+                                            chunk.deltaContent
+                                        ).right()
+                                    )
+                                }
+
+                                is LLMStreamChunk.UsageChunk -> {
+                                    finalUsage = LLMCompletionResult.UsageStats(
+                                        chunk.promptTokens,
+                                        chunk.completionTokens,
+                                        chunk.totalTokens
+                                    )
+                                }
+
+                                LLMStreamChunk.Done -> {
+                                    // 7. Stream is finished and successful. NOW save the assistant message to the DB.
+                                    val savedAssistantMessage = transactionScope.transaction {
+                                        either {
+                                            val assistantMsg = messageDao.insertAssistantMessage(
+                                                sessionId,
+                                                accumulatedContent,
+                                                userMessage.id, // Parent is user message
+                                                model.id,
+                                                settings.id
+                                            ).mapLeft { daoError ->
+                                                ProcessNewMessageError.ModelConfigurationError("Failed to insert final assistant message: ${daoError::class.simpleName}")
+                                            }.bind()
+
+                                            // Link assistant message as child to user message in DB
+                                            messageDao.addChildToMessage(userMessage.id, assistantMsg.id)
+                                                .mapLeft { linkError ->
+                                                    logger.warn("Failed to link assistant message ${assistantMsg.id} as child to user message ${userMessage.id}: ${linkError::class.simpleName}")
+                                                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link assistant message.")
+                                                }.bind()
+
+                                            // Update session's leaf message ID to the newly saved assistant message
+                                            sessionDao.updateSessionLeafMessageId(sessionId, assistantMsg.id)
+                                                .mapLeft { updateError ->
+                                                    logger.warn("Failed to update session leaf message ID to ${assistantMsg.id}: ${updateError::class.simpleName}")
+                                                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to update session leaf.")
+                                                }.bind()
+                                            assistantMsg
+                                        }
+                                    }.getOrElse { error ->
+                                        logger.error("Final assistant message save failed for session $sessionId: $error")
+                                        send(
+                                            ChatUpdate.ErrorUpdate(
+                                                ProcessNewMessageError.ExternalServiceError(
+                                                    LLMCompletionError.OtherError("Failed to save final message.")
+                                                ).toApiError()
+                                            ).right()
+                                        )
+                                        throw RuntimeException("Failed to save final message: $error") // Propagate error to outer catch
+                                    }
+
+                                    // 8. Emit the final update with the REAL database ID
+                                    send(
+                                        ChatUpdate.AssistantMessageEnd(
+                                            tempMessageId = temporaryAssistantMessageId,
+                                            finalMessage = savedAssistantMessage
+                                        ).right()
+                                    )
+                                    send(ChatUpdate.Done.right()) // Signal flow completion
+                                }
+
+                                is LLMStreamChunk.Error -> {
+                                    logger.error("LLM API returned streaming error chunk: ${chunk.llmError}")
+                                    send(
+                                        ChatUpdate.ErrorUpdate(
+                                            ProcessNewMessageError.ExternalServiceError(chunk.llmError).toApiError()
+                                        ).right()
+                                    )
+                                    throw RuntimeException("LLM streaming error: ${chunk.llmError}") // Propagate error
+                                }
+                            }
+                        }
+                    )
+                }
+        } catch (e: Exception) {
+            // This catches any errors that were `throw`n (including those from LLM API stream)
+            logger.error(
+                "Streaming message processing failed for session $sessionId due to unhandled exception: ${e.message}",
+                e
+            )
+            send(
+                ChatUpdate.ErrorUpdate(
+                    ProcessNewMessageError.ExternalServiceError(
+                        LLMCompletionError.OtherError(
+                            "An unexpected error occurred during streaming.",
+                            e
+                        )
+                    ).toApiError()
+                ).right()
+            )
+        } finally {
+            close() // Ensure the channel is closed after completion or error
+        }
+    }
+
+    private suspend fun getLlmConfig(
+        session: ChatSession,
+        modelId: Long,
+        settingsId: Long
+    ): Either<ProcessNewMessageError, LlmConfig> = either {
+        val model = llmModelService.getModelById(modelId).mapLeft { serviceError ->
+            ProcessNewMessageError.ModelConfigurationError("Model with ID $modelId not found: ${serviceError::class.simpleName}")
+        }.bind()
+
+        val settings = modelSettingsService.getSettingsById(settingsId).mapLeft { serviceError ->
+            ProcessNewMessageError.ModelConfigurationError("Settings with ID $settingsId not found: ${serviceError::class.simpleName}")
+        }.bind()
+
+        val provider = llmProviderService.getProviderById(model.providerId).mapLeft { serviceError ->
+            ProcessNewMessageError.ModelConfigurationError("Provider not found for model ID $modelId (provider ID: ${model.providerId}): ${serviceError::class.simpleName}")
+        }.bind()
+
+        val apiKey = provider.apiKeyId?.let { keyId ->
+            credentialManager.getCredential(keyId).mapLeft { credError ->
+                ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
+            }.bind()
+        }
+        LlmConfig(model, settings, provider, apiKey)
+    }
+
override suspend fun updateMessageContent(
id: Long,
content: String
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/ChatCompletionStrategy.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/ChatCompletionStrategy.kt
index 43c67f7..35f97ba 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/ChatCompletionStrategy.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/ChatCompletionStrategy.kt
@@ -2,6 +2,7 @@ package eu.torvian.chatbot.server.service.llm

import arrow.core.Either
import eu.torvian.chatbot.common.models.*
+import kotlinx.coroutines.flow.Flow

/**
* Defines the interface for provider-specific chat completion logic.
  @@ -22,6 +23,7 @@ interface ChatCompletionStrategy {
    * @param provider Configuration of the LLM provider, including base URL and type.
    * @param settings Specific generation settings for this request.
    * @param apiKey The API key string, if required by the provider and available.
+     * @param isStreaming Boolean indicating if a streaming response is requested.
      * @return Either a [LLMCompletionError.ConfigurationError] if preparation fails (e.g., missing key),
      *         or the [ApiRequestConfig] containing details for the HTTP call.
      */
@@ -30,7 +32,8 @@ interface ChatCompletionStrategy {
modelConfig: LLMModel,
provider: LLMProvider,
settings: ModelSettings,
-        apiKey: String?
+        apiKey: String?,
+        isStreaming: Boolean
  ): Either<LLMCompletionError.ConfigurationError, ApiRequestConfig>

  /**
  @@ -44,6 +47,18 @@ interface ChatCompletionStrategy {
  */
  fun processSuccessResponse(responseBody: String): Either<LLMCompletionError.InvalidResponseError, LLMCompletionResult>

+    /**
+     * Processes a raw streaming API response (as a Flow of strings/bytes) into a generic stream of LLMStreamChunk.
+     * The strategy is responsible for parsing each raw chunk according to its provider's streaming format
+     * (e.g., SSE for OpenAI, NDJSON for Ollama) and mapping it to LLMStreamChunk.
+     *
+     * @param responseStream A Flow of raw string chunks from the HTTP response.
+     * @return A Flow of Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>
+     */
+    fun processStreamingResponse(
+        responseStream: Flow<String>
+    ): Flow<Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>>
+
/**
* Processes a raw error API response body string and status code into a specific generic error ([LLMCompletionError]).
* The strategy is responsible for parsing API-specific error formats from the body string
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClient.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClient.kt
index b9ec938..a3d858a 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClient.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClient.kt
@@ -5,6 +5,7 @@ import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.ModelSettings
+import kotlinx.coroutines.flow.Flow

/**
* Interface for interacting with external LLM APIs.
  @@ -13,7 +14,7 @@ import eu.torvian.chatbot.common.models.ModelSettings
  */
  interface LLMApiClient {
  /**
-     * Sends a chat completion request to the appropriate LLM API based on the provider type.
+     * Sends a non-streaming chat completion request to the appropriate LLM API.
      * Delegates the API-specific details (request format, response parsing) to an internal strategy.
      *
      * @param messages The list of messages forming the conversation context.
@@ -31,4 +32,20 @@ interface LLMApiClient {
settings: ModelSettings,
apiKey: String?
): Either<LLMCompletionError, LLMCompletionResult>
+
+    /**
+     * Sends a streaming chat completion request to the appropriate LLM API.
+     * Delegates API-specific details to a strategy.
+     *
+     * @return A Flow of Either<LLMCompletionError, LLMStreamChunk> representing the stream.
+     *         An error emitted in the Flow indicates a problem during the stream.
+     *         The flow terminates with `LLMStreamChunk.Done` on success or an error.
+     */
+    fun completeChatStreaming(
+        messages: List<ChatMessage>,
+        modelConfig: LLMModel,
+        provider: LLMProvider,
+        settings: ModelSettings,
+        apiKey: String?
+    ): Flow<Either<LLMCompletionError, LLMStreamChunk>>
     }
     diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClientKtor.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClientKtor.kt
     index d7c030f..4bb055a 100644
     --- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClientKtor.kt
     +++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMApiClientKtor.kt
     @@ -6,9 +6,14 @@ import arrow.core.left
     import arrow.core.right
     import eu.torvian.chatbot.common.models.*
     import io.ktor.client.*
     +import io.ktor.client.call.*
     +import io.ktor.client.plugins.*
     import io.ktor.client.request.*
     import io.ktor.client.statement.*
     import io.ktor.http.*
     +import io.ktor.utils.io.*
     +import kotlinx.coroutines.flow.Flow
     +import kotlinx.coroutines.flow.channelFlow
     import org.apache.logging.log4j.LogManager
     import org.apache.logging.log4j.Logger

@@ -28,7 +33,6 @@ class LLMApiClientKtor(
private val httpClient: HttpClient,
private val strategies: Map<LLMProviderType, ChatCompletionStrategy>
) : LLMApiClient {
-
companion object {
private val logger: Logger = LogManager.getLogger(LLMApiClientKtor::class.java)
}
@@ -61,7 +65,8 @@ class LLMApiClientKtor(
modelConfig = modelConfig,
provider = provider,
settings = settings,
-            apiKey = apiKey
+            apiKey = apiKey,
+            isStreaming = false
         ).getOrElse { error -> // Handle ConfigurationError returned by the strategy
             logger.error("Strategy ${strategy::class.simpleName} failed to prepare request: ${error.message}")
             return error.left() // Propagate the specific error returned by the strategy
@@ -87,6 +92,9 @@ class LLMApiClientKtor(
// Set the request body. HttpClient's ContentNegotiation feature will
// automatically serialize the object based on the content type.
setBody(apiRequestConfig.body)
+                timeout {
+                    requestTimeoutMillis = 60000 // 60 seconds
+                }
             }
             logger.debug("Received HTTP response: ${httpResponse.status}")

@@ -133,8 +141,107 @@ class LLMApiClientKtor(
).left()
}
}
+
+    override fun completeChatStreaming(
+        messages: List<ChatMessage>,
+        modelConfig: LLMModel,
+        provider: LLMProvider,
+        settings: ModelSettings,
+        apiKey: String?
+    ): Flow<Either<LLMCompletionError, LLMStreamChunk>> = channelFlow {
+        logger.info("LLMApiClientKtor: Received streaming request for model ${modelConfig.name} (Provider: ${provider.name}, Type: ${provider.type})")
+
+        val strategy = strategies[provider.type]
+            ?: run {
+                val errorMsg = "No ChatCompletionStrategy found for provider type: ${provider.type}"
+                logger.error(errorMsg)
+                send(LLMCompletionError.ConfigurationError(errorMsg).left())
+                close()
+                return@channelFlow
+            }
+
+        val apiRequestConfig = strategy.prepareRequest(
+            messages = messages,
+            modelConfig = modelConfig,
+            provider = provider,
+            settings = settings,
+            apiKey = apiKey,
+            isStreaming = true
+        ).getOrElse { error ->
+            logger.error("Strategy ${strategy::class.simpleName} failed to prepare streaming request: ${error.message}")
+            send(error.left())
+            close()
+            return@channelFlow
+        }
+
+        try {
+            logger.debug("Executing HTTP streaming request: ${apiRequestConfig.method} ${provider.baseUrl}${apiRequestConfig.path}")
+
+            // Use preparePost and execute to get a streaming response
+            httpClient.prepareRequest("${provider.baseUrl}${apiRequestConfig.path}") {
+                method = apiRequestConfig.method.toKtorHttpMethod()
+                contentType(apiRequestConfig.contentType.toKtorContentType())
+                headers {
+                    apiRequestConfig.customHeaders.forEach { (key, value) ->
+                        append(key, value)
+                    }
+                }
+                setBody(apiRequestConfig.body)
+                timeout {
+                    requestTimeoutMillis = HttpTimeoutConfig.INFINITE_TIMEOUT_MS // Allow indefinite stream
+                }
+            }.execute { httpResponse -> // Use execute block for streaming
+                if (httpResponse.status.isSuccess()) {
+                    logger.debug("Received HTTP streaming response: ${httpResponse.status}")
+
+                    // Get the ByteReadChannel for raw content
+                    val byteReadChannel: ByteReadChannel = httpResponse.body()
+
+                    // Transform ByteReadChannel into a Flow of lines
+                    val responseStream: Flow<String> = channelFlow {
+                        while (!byteReadChannel.isClosedForRead) {
+                            val line = byteReadChannel.readUTF8Line()
+                            if (line != null) {
+                                logger.debug("Received streaming chunk: $line")
+                                send(line)
+                            } else {
+                                break // End of stream
+                            }
+                        }
+                    }
+
+                    // Process the stream using the strategy
+                    strategy.processStreamingResponse(responseStream).collect { chunkEither ->
+                        send(chunkEither) // Forward each processed chunk to the downstream flow
+                    }
+                } else {
+                    val errorBody = try {
+                        httpResponse.bodyAsText()
+                    } catch (e: Exception) {
+                        logger.error("Failed to read error response body from ${provider.name} API", e)
+                        "Failed to read error body: ${e.message}"
+                    }
+                    logger.debug("Processing error response (streaming) with strategy ${strategy::class.simpleName}")
+                    val apiError = strategy.processErrorResponse(httpResponse.status.value, errorBody)
+                    logger.error("LLM API ${provider.name} returned error (Streaming Status: ${httpResponse.status.value}): $apiError")
+                    send(apiError.left())
+                }
+            }
+        } catch (e: Exception) {
+            logger.error("LLMApiClientKtor: HTTP streaming request failed for provider ${provider.name}", e)
+            send(
+                LLMCompletionError.NetworkError(
+                    "Network or communication error with ${provider.name}: ${e.message}",
+                    e
+                ).left()
+            )
+        } finally {
+            close() // Ensure the channel is closed
+        }
+    }
     }

+
// --- Helper functions ---

/**
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMStreamChunk.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMStreamChunk.kt
new file mode 100644
index 0000000..bc774f8
--- /dev/null
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/LLMStreamChunk.kt
@@ -0,0 +1,19 @@
+package eu.torvian.chatbot.server.service.llm
+
+/**
+ * Sealed class representing a single chunk of data in a streaming LLM response.
+ * This is an internal representation, parsed by strategies from raw API responses.
+ */
  +sealed class LLMStreamChunk {
+    /** Represents a content delta chunk. */
+    data class ContentChunk(val deltaContent: String, val finishReason: String? = null) : LLMStreamChunk()
+
+    /** Represents a usage statistics chunk (might be sent at the end). */
+    data class UsageChunk(val promptTokens: Int, val completionTokens: Int, val totalTokens: Int) : LLMStreamChunk()
+
+    /** Represents the final "done" signal from the LLM. */
+    data object Done : LLMStreamChunk()
+
+    /** Represents an error encountered *during* streaming by the LLM API itself. */
+    data class Error(val llmError: LLMCompletionError) : LLMStreamChunk()
     +}
     diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaApiModels.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaApiModels.kt
     index 62f171c..597a7c3 100644
     --- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaApiModels.kt
     +++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaApiModels.kt
     @@ -51,6 +51,32 @@ object OllamaApiModels {
     )
     }

+    /**
+     * Represents a single chunk received during a streaming chat completion from Ollama.
+     * Each line in an Ollama stream is a JSON object matching this structure (except for the final 'done: true' object).
+     * The 'done' property indicates if the stream is finished.
+     * The 'message' property contains the delta content.
+     */
+    @Serializable
+    data class ChatCompletionStreamResponse(
+        val model: String,
+        val created_at: String,
+        val message: Message? = null, // message can be null for the final done chunk (which contains stats)
+        val done: Boolean,
+        val total_duration: Long? = null,
+        val load_duration: Long? = null,
+        val prompt_eval_count: Int? = null,
+        val prompt_eval_duration: Long? = null,
+        val eval_count: Int? = null,
+        val eval_duration: Long? = null
+    ) {
+        @Serializable
+        data class Message(
+            val role: String,
+            val content: String
+        )
+    }
+
/**
* Represents a chat completion request to Ollama API.
* Matches the structure documented by Ollama for non-streaming requests.
diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaChatStrategy.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaChatStrategy.kt
index f679064..02cf665 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaChatStrategy.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OllamaChatStrategy.kt
@@ -5,6 +5,7 @@ import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.llm.*
+import kotlinx.coroutines.flow.*
import kotlinx.serialization.json.*
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
@@ -28,10 +29,10 @@ class OllamaChatStrategy(private val json: Json) : ChatCompletionStrategy {
modelConfig: LLMModel,
provider: LLMProvider,
settings: ModelSettings,
-        apiKey: String?
+        apiKey: String?,
+        isStreaming: Boolean
  ): Either<LLMCompletionError.ConfigurationError, ApiRequestConfig> {
-
-        logger.debug("OllamaChatStrategy: Preparing request for model ${modelConfig.name}")
+        logger.debug("OllamaChatStrategy: Preparing request for model ${modelConfig.name} (isStreaming: $isStreaming)")

         // 1. Ollama typically doesn't require an API key for local instances
         // However, we don't validate against apiKey being provided as some setups might use it
@@ -93,7 +94,7 @@ class OllamaChatStrategy(private val json: Json) : ChatCompletionStrategy {
val requestBodyDto = OllamaApiModels.ChatCompletionRequest(
model = modelConfig.name,
messages = apiMessages,
-            stream = false, // We're using non-streaming mode
+            stream = isStreaming,
             options = options
         )

@@ -122,7 +123,8 @@ class OllamaChatStrategy(private val json: Json) : ChatCompletionStrategy {
}

     override fun processSuccessResponse(responseBody: String): Either<LLMCompletionError.InvalidResponseError, LLMCompletionResult> {
-        logger.debug("OllamaChatStrategy: Processing success response body: ${responseBody.take(500)}...") // Log part of the body
+        // This method is for *non-streaming* responses, typically used by completeChat.
+        logger.debug("OllamaChatStrategy: Processing success response body (non-streaming): ${responseBody.take(500)}...")
         return try {
             // 1. Deserialize the raw string body into the API-specific success response DTO
             val successResponse: OllamaApiModels.ChatCompletionResponse = json.decodeFromString(responseBody)
@@ -154,14 +156,47 @@ class OllamaChatStrategy(private val json: Json) : ChatCompletionStrategy {
"eval_duration" to successResponse.eval_duration
).filterValues { it != null }
)
-            logger.debug("OllamaChatStrategy: Successfully processed response to LLMCompletionResult")
+            logger.debug("OllamaChatStrategy: Successfully processed non-streaming response to LLMCompletionResult")
             result.right()

         } catch (e: Exception) {
-            // Handle any exceptions during deserialization or mapping
-            logger.error("OllamaChatStrategy: Failed to parse Ollama success response body", e)
-            LLMCompletionError.InvalidResponseError("Failed to parse Ollama success response body: ${e.message}", e)
-                .left()
+            logger.error("OllamaChatStrategy: Failed to parse Ollama non-streaming success response body", e)
+            LLMCompletionError.InvalidResponseError("Failed to parse Ollama non-streaming success response body: ${e.message}", e).left()
+        }
+    }
+
+    override fun processStreamingResponse(
+        responseStream: Flow<String>
+    ): Flow<Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>> = flow {
+        responseStream.collect { rawChunk ->
+            if (rawChunk.isBlank()) return@collect // Ignore empty lines
+
+            try {
+                // Ollama sends newline-delimited JSON objects
+                val streamResponse = json.decodeFromString<OllamaApiModels.ChatCompletionStreamResponse>(rawChunk)
+
+                if (streamResponse.done) {
+                    // This is the final chunk, might contain usage stats
+                    val usage = LLMCompletionResult.UsageStats(
+                        promptTokens = streamResponse.prompt_eval_count ?: 0,
+                        completionTokens = streamResponse.eval_count ?: 0,
+                        totalTokens = (streamResponse.prompt_eval_count ?: 0) + (streamResponse.eval_count ?: 0)
+                    )
+                    emit(LLMStreamChunk.UsageChunk(usage.promptTokens, usage.completionTokens, usage.totalTokens).right())
+                    emit(LLMStreamChunk.Done.right())
+                } else {
+                    // Regular content chunk
+                    streamResponse.message?.let { message ->
+                        emit(LLMStreamChunk.ContentChunk(message.content).right())
+                    } ?: run {
+                        // This case should ideally not happen for non-done chunks, but for safety
+                        logger.warn("OllamaChatStrategy: Received a non-done chunk without a 'message' field: $rawChunk")
+                    }
+                }
+            } catch (e: Exception) {
+                logger.error("OllamaChatStrategy: Failed to parse Ollama streaming response chunk: $rawChunk", e)
+                emit(LLMCompletionError.InvalidResponseError("Failed to parse Ollama streaming response chunk: ${e.message}", e).left())
+            }
         }
  }

diff --git a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OpenAIChatStrategy.kt b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OpenAIChatStrategy.kt
index 76506c1..37d6572 100644
--- a/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OpenAIChatStrategy.kt
+++ b/server/src/main/kotlin/eu/torvian/chatbot/server/service/llm/strategy/OpenAIChatStrategy.kt
@@ -6,6 +6,7 @@ import arrow.core.right
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.llm.*
import io.ktor.http.*
+import kotlinx.coroutines.flow.*
import kotlinx.serialization.json.*
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
@@ -29,7 +30,8 @@ class OpenAIChatStrategy(private val json: Json) : ChatCompletionStrategy {
modelConfig: LLMModel,
provider: LLMProvider,
settings: ModelSettings,
-        apiKey: String?
+        apiKey: String?,
+        isStreaming: Boolean
  ): Either<LLMCompletionError.ConfigurationError, ApiRequestConfig> {

         logger.debug("OpenAIChatStrategy: Preparing request for model ${modelConfig.name}")
@@ -203,4 +205,13 @@ class OpenAIChatStrategy(private val json: Json) : ChatCompletionStrategy {
)
}
}
+
+    override fun processStreamingResponse(
+        responseStream: Flow<String>
+    ): Flow<Either<LLMCompletionError.InvalidResponseError, LLMStreamChunk>> = flow {
+        // TODO: Implement OpenAI streaming response processing
+        // OpenAI uses Server-Sent Events (SSE) format for streaming
+        // This is a placeholder implementation
+        emit(LLMCompletionError.InvalidResponseError("OpenAI streaming not yet implemented", null).left())
+    }
     }
```