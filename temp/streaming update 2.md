### Client Module Files

**1. `chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ChatApi.kt`**
(Added `processNewMessageStreaming` method, kept non-streaming one)

```kotlin
package eu.torvian.chatbot.app.service.api
import arrow.core.Either
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest

/**
 * Frontend API interface for interacting with Chat message-related endpoints.
 *
 * This interface defines the operations available for managing individual messages
 * and processing new messages within a session. Implementations use the internal HTTP API.
 * All methods are suspend functions and return [Either<ApiError, T>] to explicitly
 * handle potential API errors.
 */
interface ChatApi {
    /**
     * Sends a new user message to a specified session and processes the LLM response.
     * This is for **non-streaming** responses.
     *
     * Corresponds to `POST /api/v1/sessions/{sessionId}/messages` with `stream=false`.
     *
     * @param sessionId The ID of the session to send the message to.
     * @param request The details of the new message, including content and optional parent ID.
     * @return [arrow.core.Either.Right] containing a list with the newly created user and assistant messages (in that order) on success,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun processNewMessage(sessionId: Long, request: ProcessNewMessageRequest): Either<ApiError, List<ChatMessage>>

    /**
     * Sends a new user message to a specified session and streams the LLM response back.
     *
     * Corresponds to `POST /api/v1/sessions/{sessionId}/messages` with `stream=true`.
     *
     * @param sessionId The ID of the session to send the message to.
     * @param request The details of the new message.
     * @return A [Flow] of [Either<ApiError, ChatUpdate>] representing the stream of updates.
     *         The flow will emit various [ChatUpdate] types until [ChatUpdate.Done] or an error.
     */
    fun processNewMessageStreaming(sessionId: Long, request: ProcessNewMessageRequest): Flow<Either<ApiError, ChatUpdate>>

    /**
     * Updates the content of an existing message.
     *
     * Corresponds to `PUT /api/v1/messages/{messageId}/content`.
     * (E3.S3)
     *
     * @param messageId The ID of the message to update.
     * @param request The request body containing the new content.
     * @return [Either.Right] containing the updated [ChatMessage] object on success,
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun updateMessageContent(messageId: Long, request: UpdateMessageRequest): Either<ApiError, ChatMessage>

    /**
     * Deletes a specific message and its children.
     *
     * Corresponds to `DELETE /api/v1/messages/{messageId}`.
     * (E3.S4)
     *
     * @param messageId The ID of the message to delete.
     * @return [Either.Right] with [Unit] on successful deletion (typically HTTP 204 No Content),
     *         or [Either.Left] containing an [ApiError] on failure.
     */
    suspend fun deleteMessage(messageId: Long): Either<ApiError, Unit>
}
```

**2. `chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorChatApiClient.kt`**
(Implemented `processNewMessageStreaming` using `bodyAsChannel` and SSE parsing)

```kotlin
package eu.torvian.chatbot.app.service.api.ktor
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.api.resources.MessageResource
import eu.torvian.chatbot.common.api.resources.SessionResource
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.utils.io.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
/**
 * Ktor HttpClient implementation of the [ChatApi] interface.
 *
 * Uses the configured [HttpClient] and the [BaseApiClient.safeApiCall] helper
 * to interact with the backend's chat message endpoints, mapping responses
 * to [Either<ApiError, T>].
 *
 * @property client The Ktor HttpClient instance injected for making requests.
 */
class KtorChatApiClient(client: HttpClient) : BaseApiClient(client), ChatApi {
    companion object {
        private val logger = LogManager.getLogger(KtorChatApiClient::class.java)
    }

    // Need a Json instance for deserialization.
    // Ensure this matches the configuration of the Ktor client's ContentNegotiation.
    // It's best practice to inject this if your DI framework supports it.
    private val json: Json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        encodeDefaults = true // Important for requests
        classDiscriminator = "role" // For ChatMessage sealed class deserialization
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        request: ProcessNewMessageRequest
    ): Either<ApiError, List<ChatMessage>> {
        // Use safeApiCall to wrap the Ktor request
        return safeApiCall {
            // Use Ktor resources to build the URL: /api/v1/sessions/{sessionId}/messages
            client.post(SessionResource.ById.Messages(SessionResource.ById(SessionResource(), sessionId))) {
                // Set the request body with the ProcessNewMessageRequest DTO
                setBody(request)
            }.body<List<ChatMessage>>() // Expect a List<ChatMessage> in the response body on success (HTTP 201)
        }
    }

    override fun processNewMessageStreaming(
        sessionId: Long,
        request: ProcessNewMessageRequest
    ): Flow<Either<ApiError, ChatUpdate>> = flow {
        try {
            val httpResponse: HttpResponse = client.post(SessionResource.ById.Messages(SessionResource.ById(SessionResource(), sessionId))) {
                setBody(request)
                // Explicitly request SSE content type if not already default
                accept(ContentType.Text.EventStream)
                timeout {
                    requestTimeoutMillis = HttpTimeout.INFINITE_TIMEOUT_MS // Allow indefinite stream
                }
            }

            if (httpResponse.status.isSuccess()) {
                val channel: ByteReadChannel = httpResponse.bodyAsChannel()
                while (!channel.isClosedForRead) {
                    val line = channel.readUTF8Line()
                    if (line == null || line.isBlank()) continue // Skip empty lines

                    // SSE format: "data: {json_payload}\n"
                    if (line.startsWith("data:")) {
                        val data = line.removePrefix("data:").trim()
                        if (data.isNotBlank()) {
                            try {
                                val chatUpdate = json.decodeFromString<ChatUpdate>(data)
                                emit(chatUpdate.right())
                                if (chatUpdate is ChatUpdate.Done || chatUpdate is ChatUpdate.ErrorUpdate) {
                                    // Explicitly stop collecting if done or error event received
                                    break
                                }
                            } catch (e: Exception) {
                                logger.error("Failed to parse SSE data chunk for session $sessionId: '$data'", e)
                                emit(ApiError.ParsingError("Failed to parse chat update chunk", e.message).left())
                                break // Stop on parsing error
                            }
                        }
                    } else if (line.startsWith("event:")) {
                        // You can read the event type if needed, e.g., val eventType = line.removePrefix("event:").trim()
                    }
                    // Ignore other SSE fields like "id:", "retry:", etc.
                }
            } else {
                // Handle non-2xx HTTP status codes immediately
                val errorBody = httpResponse.bodyAsText()
                val apiError = parseApiError(httpResponse.status.value, errorBody)
                logger.error("API call for session $sessionId failed with status ${httpResponse.status.value}: $apiError")
                emit(apiError.left())
            }
        } catch (e: Exception) {
            logger.error("Network or streaming error during processNewMessageStreaming for session $sessionId", e)
            emit(ApiError.NetworkError("Network or communication error during streaming: ${e.message}", e.message).left())
        }
    }

    override suspend fun updateMessageContent(
        messageId: Long,
        request: UpdateMessageRequest
    ): Either<ApiError, ChatMessage> {
        // Use safeApiCall to wrap the Ktor request
        return safeApiCall {
            // Use Ktor resources to build the URL: /api/v1/messages/{messageId}/content
            client.put(MessageResource.ById.Content(MessageResource.ById(MessageResource(), messageId))) {
                // Set the request body with the UpdateMessageRequest DTO
                setBody(request)
            }.body<ChatMessage>() // Expect a ChatMessage in the response body on success (HTTP 200)
        }
    }

    override suspend fun deleteMessage(messageId: Long): Either<ApiError, Unit> {
        // Use safeApiCall to wrap the Ktor request
        return safeApiCall {
            // Use Ktor resources to build the URL: /api/v1/messages/{messageId}
            client.delete(MessageResource.ById(MessageResource(), messageId))
                // The backend should return HTTP 204 No Content on success.
                // Ktor's body<Unit>() can be used, or simply letting the request complete
                // indicates success for Unit return type in safeApiCall.
                .body<Unit>() // Explicitly expect Unit body on success
        }
    }
}
```

**3. `chatbot/app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ChatViewModel.kt`**
(Main logic for handling streaming, temporary messages, and performance)

```kotlin
package eu.torvian.chatbot.app.viewmodel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.domain.events.SnackbarInteractionEvent
import eu.torvian.chatbot.app.domain.events.apiRequestError
import eu.torvian.chatbot.app.generated.resources.Res
import eu.torvian.chatbot.app.generated.resources.error_loading_session
import eu.torvian.chatbot.app.generated.resources.error_sending_message_short
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.app.service.api.SettingsApi // Import SettingsApi
import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.*
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atZone
import kotlinx.serialization.json.Json // For parsing customParamsJson
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import org.jetbrains.compose.resources.getString

/**
 * Manages the UI state for the main chat area of the currently active session,
 * using KMP ViewModel, StateFlow, Arrow's Either, and UiState for loading states.
 *
 * This class is responsible for:
 * - Holding the state (Idle/Loading/Success/Error) of the current session and its messages.
 * - Structuring the flat list of messages into a threaded view for display using Flows.
 * - Managing the state of the message input area.
 * - Handling user actions like sending messages, replying, editing, and deleting messages.
 * - Communicating with the backend via the ChatApi and SessionApi, handling their Either results.
 * - Managing the currently displayed thread branch based on the session's leaf message state.
 *
 * @constructor
 * @param sessionApi The API client for session-related operations.
 * @param chatApi The API client for chat message-related operations.
 * @param settingsApi The API client for model settings.
 * @param eventBus The event bus for emitting global events like retry-able errors.
 * @param uiDispatcher The dispatcher to use for UI-related coroutines. Defaults to Main.
 * @param clock The clock to use for timestamping. Defaults to System clock.
 *
 * @property sessionState The state of the currently loaded chat session, including all messages.
 * @property currentBranchLeafId The ID of the leaf message in the currently displayed thread branch.
 * @property displayedMessages The list of messages to display in the UI, representing the currently selected thread branch.
 * @property inputContent The current text content in the message input field.
 * @property replyTargetMessage The message the user is currently explicitly replying to via the Reply action.
 * @property editingMessage The message currently being edited (E3.S1, E3.S2).
 * @property editingContent The content of the message currently being edited (E3.S1, E3.S2).
 */
class ChatViewModel(
    private val sessionApi: SessionApi,
    private val chatApi: ChatApi,
    private val settingsApi: SettingsApi, // INJECTED
    private val eventBus: EventBus,
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main,
    private val clock: Clock = Clock.System
) : ViewModel() {
    private val logger = kmpLogger<ChatViewModel>()

    // --- Observable State for Compose UI (using StateFlow) ---
    private val _sessionState = MutableStateFlow<UiState<ApiError, ChatSession>>(UiState.Idle)
    /**
     * The state of the currently loaded chat session, including all messages.
     * When in Success state, provides the ChatSession object.
     */
    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = _sessionState.asStateFlow()

    private val _currentBranchLeafId = MutableStateFlow<Long?>(null)
    /**
     * The ID of the leaf message in the currently displayed thread branch.
     * Changing this triggers the UI to show a different branch.
     * Null if the session is empty or not loaded/successful.
     */
    val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()

    // New state to hold the actively streaming assistant message
    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)

    /**
     * The list of messages to display in the UI, representing the currently selected thread branch.
     * This is derived from the session's full list of messages and the current leaf message ID,
     * combined with any actively streaming message.
     */
    val displayedMessages: StateFlow<List<ChatMessage>> = combine(
        _sessionState.filterIsInstance<UiState.Success<ChatSession>>()
            .map { it.data.messages }, // Flow of just the message list when in Success state
        _currentBranchLeafId, // Flow of the current leaf ID
        _streamingAssistantMessage // Include the streaming message flow
    ) { allPersistedMessages, leafId, streamingAssistantMessage ->
        // This is where the UI state logic (E1.S5) happens:
        // Prepare the list of messages for `buildThreadBranch`.
        val messagesForBranching = if (streamingAssistantMessage != null) {
            // If streaming, create a copy of the persisted messages
            // and replace/add the streaming message for real-time display.
            // This ensures `buildThreadBranch` always sees the latest state.
            val updatedMessages = allPersistedMessages.toMutableList()
            val existingIndex = updatedMessages.indexOfFirst { it.id == streamingAssistantMessage.id }
            if (existingIndex != -1) {
                updatedMessages[existingIndex] = streamingAssistantMessage
            } else {
                updatedMessages.add(streamingAssistantMessage)
            }
            updatedMessages.toList()
        } else {
            allPersistedMessages
        }
        buildThreadBranch(messagesForBranching, leafId)
    }
        .stateIn(
            scope = CoroutineScope(viewModelScope.coroutineContext + uiDispatcher),
            started = SharingStarted.Eagerly,
            initialValue = emptyList()
        )

    private val _inputContent = MutableStateFlow("")
    /**
     * The current text content in the message input field.
     */
    val inputContent: StateFlow<String> = _inputContent.asStateFlow()

    private val _replyTargetMessage = MutableStateFlow<ChatMessage?>(null)
    /**
     * The message the user is currently explicitly replying to via the Reply action (E1.S7).
     * If null, sending a message replies to the [currentBranchLeafId] value.
     */
    val replyTargetMessage: StateFlow<ChatMessage?> = _replyTargetMessage.asStateFlow()

    private val _editingMessage = MutableStateFlow<ChatMessage?>(null)
    /**
     * The message currently being edited (E3.S1, E3.S2). Null if no message is being edited.
     */
    val editingMessage: StateFlow<ChatMessage?> = _editingMessage.asStateFlow()

    private val _editingContent = MutableStateFlow("")
    /**
     * The content of the message currently being edited (E3.S1, E3.S2).
     */
    val editingContent: StateFlow<String> = _editingContent.asStateFlow()

    private val _isSendingMessage = MutableStateFlow(false)
    /**
     * Indicates whether a message is currently in the process of being sent. (E1.S3)
     */
    val isSendingMessage: StateFlow<Boolean> = _isSendingMessage.asStateFlow()

    // Store the ID of the last emitted error if a retry is possible
    private val _lastFailedLoadEventId = MutableStateFlow<String?>(null)
    // Store the session ID for retry functionality
    private val _lastAttemptedSessionId = MutableStateFlow<Long?>(null)

    // For parsing customParamsJson. Ensure this Json instance matches your Ktor client setup
    private val jsonParser: Json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    init {
        // ViewModel can listen to the EventBus for its own emitted event's responses
        viewModelScope.launch(uiDispatcher) {
            eventBus.events.collect { event ->
                if (event is SnackbarInteractionEvent && event.originalAppEventId == _lastFailedLoadEventId.value) {
                    if (event.isActionPerformed) {
                        logger.info("Retrying loadSession due to Snackbar action!")
                        _lastFailedLoadEventId.value = null // Clear ID before retrying
                        _lastAttemptedSessionId.value?.let { sessionId ->
                            loadSession(sessionId, forceReload = true) // Trigger retry
                        }
                    } else { // It was dismissed (by user or timeout)
                        logger.info("Snackbar dismissed, not retrying loadSession.")
                        _lastFailedLoadEventId.value = null
                        _lastAttemptedSessionId.value = null
                    }
                }
            }
        }
    }

    // --- Public Action Functions (Called by UI Components) ---

    /**
     * Loads a chat session and its messages by ID.
     * Triggered when a session is selected in the session list (E2.S4).
     *
     * @param sessionId The ID of the session to load, or null to clear the session.
     * @param forceReload If true, reloads the session even if it's already loaded successfully.
     */
    fun loadSession(sessionId: Long?, forceReload: Boolean = false) {
        // Prevent reloading if already loading or if the session is already loaded successfully
        val currentState = _sessionState.value
        if (!forceReload && (currentState.isLoading || (currentState.dataOrNull?.id == sessionId))) return
        if (sessionId == null) {
            clearSession()
            return
        }
        // Store the session ID for potential retry
        _lastAttemptedSessionId.value = sessionId
        viewModelScope.launch(uiDispatcher) {
            _sessionState.value = UiState.Loading
            _replyTargetMessage.value = null
            _editingMessage.value = null
            _currentBranchLeafId.value = null
            _streamingAssistantMessage.value = null // Clear any pending streaming message
            sessionApi.getSessionDetails(sessionId)
                .fold(
                    ifLeft = { error ->
                        // Handle Error case (E1.S6)
                        _sessionState.value = UiState.Error(error)
                        // Emit to generic EventBus using the specific error type
                        val globalError = apiRequestError(
                            apiError = error,
                            shortMessage = getString(Res.string.error_loading_session),
                            isRetryable = true
                        )
                        _lastFailedLoadEventId.value = globalError.eventId // Store its ID
                        eventBus.emitEvent(globalError)
                    },
                    ifRight = { session ->
                        // Handle Success case (E2.S4)
                        _sessionState.value = UiState.Success(session) // Success payload is the ChatSession
                        // Set initial leaf to the session's saved leaf ID or null if no messages
                        _currentBranchLeafId.value = session.currentLeafMessageId
                        // Clear retry state on success
                        _lastFailedLoadEventId.value = null
                        _lastAttemptedSessionId.value = null
                    }
                )
        }
    }

    /**
     * Clears the currently loaded session state.
     * Called when the selected session is deleted or potentially on app exit.
     */
    fun clearSession() {
        _sessionState.value = UiState.Idle // Go back to idle/no session state
        _replyTargetMessage.value = null
        _editingMessage.value = null
        _currentBranchLeafId.value = null
        _streamingAssistantMessage.value = null // Clear streaming message on session clear
    }

    /**
     * Updates the content of the message input field.
     *
     * @param newText The new text from the input field.
     */
    fun updateInput(newText: String) {
        _inputContent.value = newText
    }

    /**
     * Sends the current message content to the active session.
     * Determines the parent based on [replyTargetMessage] or [currentBranchLeafId].
     * Decides between streaming and non-streaming based on ModelSettings.
     * (E1.S1, E1.S7)
     */
    fun sendMessage() {
        val currentSession = _sessionState.value.dataOrNull ?: return // Cannot send if no session loaded successfully
        val content = _inputContent.value.trim()
        if (content.isBlank()) return // Cannot send empty message

        val parentId = _replyTargetMessage.value?.id ?: _currentBranchLeafId.value // Use value from StateFlow

        viewModelScope.launch(uiDispatcher) {
            _isSendingMessage.value = true // Set sending state to true (E1.S3)
            _inputContent.value = "" // Clear input field immediately for responsiveness
            _replyTargetMessage.value = null // Reset reply target immediately
            _streamingAssistantMessage.value = null // Clear any previous streaming message

            val currentModelSettingsId = currentSession.currentSettingsId
            val streamPreference = if (currentModelSettingsId != null) {
                settingsApi.getSettingsById(currentModelSettingsId).fold(
                    ifLeft = { error ->
                        logger.error("Failed to fetch settings for stream preference, defaulting to streaming: $error")
                        true // Default to streaming on error
                    },
                    ifRight = { settings ->
                        // Attempt to parse 'stream' from customParamsJson
                        settings.customParamsJson?.let { customJson ->
                            try {
                                jsonParser.parseToJsonElement(customJson).jsonObject["stream"]?.jsonPrimitive?.booleanOrNull
                            } catch (e: Exception) {
                                logger.warn("Failed to parse 'stream' from customParamsJson, defaulting to true: $e")
                                null
                            }
                        } ?: true // Default to true if customParamsJson or 'stream' is null
                    }
                )
            } else {
                true // Default to streaming if no settings selected
            }

            try {
                if (streamPreference == true) { // Explicitly check for true, null defaults to true
                    chatApi.processNewMessageStreaming(
                        sessionId = currentSession.id,
                        request = ProcessNewMessageRequest(content = content, parentMessageId = parentId, stream = true)
                    ).collect { eitherUpdate ->
                        handleChatUpdate(eitherUpdate, -1L) // -1L is the temporary client-side ID
                    }
                } else {
                    chatApi.processNewMessage(
                        sessionId = currentSession.id,
                        request = ProcessNewMessageRequest(content = content, parentMessageId = parentId, stream = false)
                    ).fold(
                        ifLeft = { error -> handleApiError(error) },
                        ifRight = { newMessages ->
                            // Add both user and assistant message at once
                            addMessagesToSessionState(newMessages)
                            _currentBranchLeafId.value = newMessages.lastOrNull()?.id // Assume assistant message is the new leaf
                            _isSendingMessage.value = false // Response complete
                        }
                    )
                }
            } catch (e: Exception) {
                logger.error("Unexpected error during sending message: ${e.message}", e)
                eventBus.emitEvent(
                    apiRequestError(
                        apiError = ApiError.NetworkError("Sending message failed", e.message),
                        shortMessage = getString(Res.string.error_sending_message_short),
                        isRetryable = false
                    )
                )
                _streamingAssistantMessage.value = null
                _isSendingMessage.value = false
            } finally {
                _isSendingMessage.value = false // Ensure sending state is reset
            }
        }
    }

    /**
     * Handles incoming streaming chat updates and updates UI state.
     */
    private fun handleChatUpdate(update: Either<ApiError, ChatUpdate>, tempAssistantMessageId: Long) {
        update.fold(
            ifLeft = { error -> handleApiError(error) }, // Centralized error handling
            ifRight = { chatUpdate ->
                val currentSession = _sessionState.value.dataOrNull ?: return@fold

                when (chatUpdate) {
                    is ChatUpdate.UserMessageUpdate -> {
                        addMessagesToSessionState(listOf(chatUpdate.message))
                        _currentBranchLeafId.value = chatUpdate.message.id
                    }
                    is ChatUpdate.AssistantMessageStart -> {
                        // Create a new temporary assistant message and set it as the streaming one.
                        // It will appear in displayedMessages via `combine` and `_streamingAssistantMessage` flow.
                        val newTempAssistantMessage = ChatMessage.AssistantMessage(
                            id = tempAssistantMessageId, // Use the client-side temporary ID
                            sessionId = chatUpdate.sessionId,
                            content = "", // Start with empty content
                            createdAt = chatUpdate.createdAt,
                            updatedAt = chatUpdate.createdAt,
                            parentMessageId = chatUpdate.parentId,
                            childrenMessageIds = emptyList(), // No children initially
                            modelId = chatUpdate.modelId,
                            settingsId = chatUpdate.settingsId
                        )
                        _streamingAssistantMessage.value = newTempAssistantMessage
                        _currentBranchLeafId.value = tempAssistantMessageId // Make this the current leaf for the branch
                    }
                    is ChatUpdate.AssistantMessageDelta -> {
                        // Append content to the *current* streaming assistant message.
                        _streamingAssistantMessage.update { current ->
                            if (current?.id == chatUpdate.messageId) {
                                current.copy(content = current.content + chatUpdate.deltaContent, updatedAt = clock.now())
                            } else {
                                current // Should not happen if messageId matches the temporary one
                            }
                        }
                    }
                    is ChatUpdate.AssistantMessageEnd -> {
                        // The stream is complete and the final message has been persisted on the server.
                        // Replace the temporary message in the session's overall message list with the final, real one.
                        val updatedMessages = currentSession.messages.map {
                            if (it.id == chatUpdate.tempMessageId) chatUpdate.finalMessage else it
                        }
                        _sessionState.value = UiState.Success(
                            currentSession.copy(
                                messages = updatedMessages,
                                currentLeafMessageId = chatUpdate.finalMessage.id, // Update leaf to real ID
                                updatedAt = clock.now()
                            )
                        )
                        _currentBranchLeafId.value = chatUpdate.finalMessage.id // Ensure our leaf state reflects the real ID
                        _streamingAssistantMessage.value = null // Clear the streaming message
                        _isSendingMessage.value = false // Streaming response complete
                    }
                    is ChatUpdate.ErrorUpdate -> {
                        handleApiError(chatUpdate.error)
                        _streamingAssistantMessage.value = null // Clear streaming message on error
                        _isSendingMessage.value = false
                    }
                    ChatUpdate.Done -> {
                        // This signal should mostly be handled by AssistantMessageEnd, ensuring _isSendingMessage is false.
                        logger.info("Chat streaming completed.")
                        _isSendingMessage.value = false // Redundant but safe
                    }
                }
            }
        )
    }

    /** Centralized API error handling */
    private fun handleApiError(error: ApiError) {
        logger.error("API Error: ${error.code} - ${error.message}")
        viewModelScope.launch { // Launch in a new coroutine if needed from non-suspend context
            eventBus.emitEvent(
                apiRequestError(
                    apiError = error,
                    shortMessage = getString(Res.string.error_sending_message_short),
                    isRetryable = false
                )
            )
        }
    }

    /** Helper to add messages to the session's state. */
    private fun addMessagesToSessionState(messagesToAdd: List<ChatMessage>) {
        _sessionState.update { currentState ->
            if (currentState is UiState.Success) {
                currentState.copy(data = currentState.data.copy(
                    messages = currentState.data.messages + messagesToAdd,
                    updatedAt = clock.now()
                ))
            } else {
                currentState // Don't modify if not in Success state
            }
        }
    }

    /**
     * Sets the state to indicate the user is replying to a specific message (E1.S7).
     *
     * @param message The message to reply to.
     */
    fun startReplyTo(message: ChatMessage) {
        _replyTargetMessage.value = message
        // Optionally, trigger scroll action in UI
    }

    /**
     * Cancels the specific reply target, reverting to replying to the current leaf (E1.S7).
     */
    fun cancelReply() {
        _replyTargetMessage.value = null
    }

    /**
     * Sets the state to indicate a message is being edited (E3.S1, E3.S2).
     *
     * @param message The message to edit.
     */
    fun startEditing(message: ChatMessage) {
        _editingMessage.value = message
        _editingContent.value = message.content
    }

    /**
     * Updates the content of the message currently being edited (E3.S1, E3.S2).
     * Called by the UI as the user types in the editing input field.
     *
     * @param newText The new text content for the editing field.
     */
    fun updateEditingContent(newText: String) {
        _editingContent.value = newText
    }

    /**
     * Saves the edited message content (E3.S3).
     */
    fun saveEditing() {
        val messageToEdit = _editingMessage.value ?: return
        val newContent = _editingContent.value.trim()
        if (newContent.isBlank()) {
            // Show inline validation error (UI concern) or update state
            println("Validation Error: Message content cannot be empty.")
            return
        }
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            // Optionally show inline loading/saving state for the specific message being edited
            chatApi.updateMessageContent(messageToEdit.id, UpdateMessageRequest(newContent))
                .fold(
                    ifLeft = { error ->
                        println("Edit message API error: ${error.code} - ${error.message}")
                        // Show inline error for the edited message (UI concern)
                    },
                    ifRight = { updatedMessage ->
                        // Update the message in the messages list within the current session state Flow (E3.S3)
                        val updatedAllMessages = currentSession.messages.map {
                            if (it.id == updatedMessage.id) updatedMessage else it
                        }
                        _sessionState.value = UiState.Success(
                            currentSession.copy(
                                messages = updatedAllMessages,
                                updatedAt = clock.now()
                            )
                        )
                        // displayedMessages StateFlow derived state will react
                        // Clear editing state on success
                        _editingMessage.value = null
                        _editingContent.value = ""
                    }
                )
        }
    }

    /**
     * Cancels the message editing state (E3.S1, E3.S2).
     */
    fun cancelEditing() {
        _editingMessage.value = null
        _editingContent.value = ""
    }

    /**
     * Deletes a specific message and its children from the session (E3.S4).
     *
     * @param messageId The ID of the message to delete.
     */
    fun deleteMessage(messageId: Long) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            // Optionally show inline loading state for the specific message being deleted
            chatApi.deleteMessage(messageId)
                .fold(
                    ifLeft = { error ->
                        println("Delete message API error: ${error.code} - ${error.message}")
                        // Show transient error message
                    },
                    ifRight = {
                        // Backend handled deletion recursively (E3.S4).
                        // Reload the session to update the UI state correctly (V1.1 strategy).
                        // This action launches a new coroutine within viewModelScope.
                        loadSession(currentSession.id, forceReload = true)
                    }
                )
        }
    }

    /**
     * Switches the currently displayed chat branch to the one that includes the given message ID.
     * The ViewModel will find the actual leaf message of this branch by traversing down
     * the path of first children starting from the provided `targetMessageId`.
     * This new leaf message ID is then persisted to the session record (E1.S5).
     *
     * @param targetMessageId The ID of the message that serves as the starting point for
     *                        determining the new displayed branch. This message itself may be
     *                        a root, middle, or leaf message in the conversation tree.
     */
    fun switchBranchToMessage(targetMessageId: Long) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        if (_currentBranchLeafId.value == targetMessageId) return
        val messageMap = currentSession.messages.associateBy { it.id }
        // Use the new helper function to find the actual leaf ID
        val finalLeafId = findLeafOfBranch(targetMessageId, messageMap)
        if (finalLeafId == null) {
            println("Warning: Could not determine a valid leaf for branch starting with $targetMessageId.")
            return
        }
        if (_currentBranchLeafId.value == finalLeafId) return // Already on this exact branch
        viewModelScope.launch(uiDispatcher) {
            // Optimistically update UI state Flow first for responsiveness
            _currentBranchLeafId.value = finalLeafId
            // Persist the change to the backend (E1.S5 requirement)
            sessionApi.updateSessionLeafMessage(currentSession.id, UpdateSessionLeafMessageRequest(finalLeafId))
                .fold(
                    ifLeft = { error ->
                        println("Update leaf message API error: ${error.code} - ${error.message}")
                        // Decide rollback strategy if needed, or just show a transient error message.
                    },
                    ifRight = {
                        // Persistence successful.
                        // We should also update the leafMessageId in the session object itself
                        // within the state Flow to keep the ChatSession data consistent.
                        _sessionState.value = UiState.Success(
                            currentSession.copy(currentLeafMessageId = finalLeafId)
                        )
                    }
                )
        }
    }

    /**
     * Sets the selected model for the current session (E4.S7).
     *
     * @param modelId The ID of the model to select, or null to unset.
     */
    fun selectModel(modelId: Long?) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            sessionApi.updateSessionModel(currentSession.id, UpdateSessionModelRequest(modelId))
                .fold(
                    ifLeft = { error ->
                        println("Update session model API error: ${error.code} - ${error.message}")
                        // Show error
                    },
                    ifRight = {
                        // Update the session object within the state Flow manually as backend doesn't return it
                        _sessionState.value = UiState.Success(
                            currentSession.copy(currentModelId = modelId)
                        )
                    }
                )
        }
    }

    /**
     * Sets the selected settings profile for the current session (E4.S7).
     *
     * @param settingsId The ID of the settings profile to select, or null to unset.
     */
    fun selectSettings(settingsId: Long?) {
        val currentSession = _sessionState.value.dataOrNull ?: return
        viewModelScope.launch(uiDispatcher) {
            sessionApi.updateSessionSettings(currentSession.id, UpdateSessionSettingsRequest(settingsId))
                .fold(
                    ifLeft = { error ->
                        println("Update session settings API error: ${error.code} - ${error.message}")
                        // Show error
                    },
                    ifRight = {
                        // Update the session object within the state Flow manually
                        _sessionState.value = UiState.Success(
                            currentSession.copy(currentSettingsId = settingsId)
                        )
                    }
                )
        }
    }

    /**
     * Finds the ultimate leaf message ID by traversing down the
     * first child path from a given starting message ID.
     *
     * @param startMessageId The ID of the message to start the traversal from.
     * @param messageMap A map of all messages in the session for efficient lookup.
     * @return The ID of the leaf message found, or null if the startMessageId is invalid or a cycle/broken link is detected.
     */
    private fun findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long? {
        var currentPathMessage: ChatMessage? = messageMap[startMessageId]
        if (currentPathMessage == null) {
            println("Warning: Starting message for branch traversal not found: $startMessageId")
            return null
        }
        var finalLeafId: Long = startMessageId
        val visitedIds = mutableSetOf<Long>() // To detect cycles and prevent infinite loops
        while (currentPathMessage?.childrenMessageIds?.isNotEmpty() == true) {
            if (!visitedIds.add(currentPathMessage.id)) {
                // Cycle detected
                println("Warning: Cycle detected in message thread path at message ID: ${currentPathMessage.id}. Aborting traversal.")
                break
            }
            // Select the first child to traverse down
            val firstChildId = currentPathMessage.childrenMessageIds.first()
            currentPathMessage = messageMap[firstChildId]
            if (currentPathMessage == null) {
                // Data inconsistency: a child ID exists but the message is not in the map
                println("Warning: Child message $firstChildId not found during branch traversal. Using last valid message as leaf.")
                break
            }
            finalLeafId = currentPathMessage.id
        }
        return finalLeafId
    }

    /**
     * Utility function to build the list of messages for a specific branch (E1.S5).
     * Operates on the flat list of messages provided.
     *
     * @param allMessages The flat list of all messages in the session.
     * @param leafId The ID of the desired leaf message for the branch.
     * @return An ordered list of messages from the root of the branch down to the leaf.
     */
    private fun buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): List<ChatMessage> {
        if (leafId == null || allMessages.isEmpty()) return emptyList()
        val messageMap = allMessages.associateBy { it.id }
        val branch = mutableListOf<ChatMessage>()
        var currentMessageId: Long? = leafId
        val visitedIds = mutableSetOf<Long>() // Added for cycle detection
        // Traverse upwards from the leaf to the root
        while (currentMessageId != null) {
            val message = messageMap[currentMessageId]
            if (message == null) {
                // This indicates a data inconsistency
                println("Warning: Could not find message with ID $currentMessageId while building branch. Aborting traversal.")
                return emptyList() // Return empty as the branch is incomplete/corrupt
            }
            if (!visitedIds.add(message.id)) {
                // Cycle detected during upward traversal
                println("Warning: Cycle detected in message thread path during upward traversal at message ID: ${message.id}. Aborting traversal.")
                return emptyList() // Return empty as the branch is corrupted
            }
            branch.add(message)
            currentMessageId = message.parentMessageId
        }
        // Reverse the list to get the correct order (root to leaf)
        return branch.reversed()
    }
}
```

---

**Initial Ktor Server Configuration (Example)**

Make sure your Ktor server's `Application.module()` (or similar configuration file) includes the necessary features:

```kotlin
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.sse.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json
import org.koin.ktor.plugin.Koin // Assuming Koin is your DI framework
import org.koin.logger.slf4j.Slf4jLogger // For Koin logging

fun Application.module() {
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
            encodeDefaults = true // Crucial for sending 'stream: false' in requests
            classDiscriminator = "role" // Required for sealed classes like ChatMessage
        })
    }
    install(SSE) // Install SSE feature

    // If using Koin for dependency injection
    install(Koin) {
        Slf4jLogger() // Or other logger
        modules(
            // Your other Koin modules
            // For example, make sure your Json instance is provided to other classes that need it
            // single { Json { /* ... config ... */ } }
        )
    }

    // configureRouting() // Your routing setup, which calls configureSessionRoutes
}
```