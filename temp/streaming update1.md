**9. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/MessageService.kt`**
(Updated `processNewMessage` to be non-streaming, added `processNewMessageStreaming`)

```kotlin
package eu.torvian.chatbot.server.service.core
import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.service.core.error.message.DeleteMessageError
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.message.UpdateMessageContentError
import kotlinx.coroutines.flow.Flow

/**
 * Service interface for managing Chat Messages and their threading relationships.
 * Contains core business logic for message processing and modification.
 */
interface MessageService {
    /**
     * Retrieves a list of all messages for a specific session, ordered by creation time.
     * @param sessionId The ID of the session.
     * @return A list of [ChatMessage] objects.
     */
    suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage>

    /**
     * Processes a new incoming user message and awaits the full LLM response.
     * The assistant message is saved only after the full response is received.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to (null for root messages).
     * @return Either a [ProcessNewMessageError] if processing fails,
     *         or a list containing the newly created user and assistant messages ([userMsg, assistantMsg]).
     */
    suspend fun processNewMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long? = null
    ): Either<ProcessNewMessageError, List<ChatMessage>>

    /**
     * Processes a new incoming user message and streams the LLM response.
     * The user message is saved immediately. The assistant message is saved only upon successful completion of the stream.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to.
     * @return A Flow of Either<ProcessNewMessageError, ChatUpdate>.
     *         The flow emits `ChatUpdate` events (user message, assistant start, deltas, end)
     *         or `ProcessNewMessageError` if an error occurs during streaming.
     */
    fun processNewMessageStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long? = null
    ): Flow<Either<ProcessNewMessageError, ChatUpdate>>

    /**
     * Updates the content of an existing message.
     * @param id The ID of the message to update.
     * @param content The new content.
     * @return Either an [UpdateMessageContentError] if the message doesn't exist,
     *         or the updated [ChatMessage].
     */
    suspend fun updateMessageContent(id: Long, content: String): Either<UpdateMessageContentError, ChatMessage>

    /**
     * Deletes a specific message and its children recursively.
     * Updates the parent's children list.
     * @param id The ID of the message to delete.
     * @return Either a [DeleteMessageError] if the message doesn't exist, or Unit if successful.
     */
    suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit>
}
```

**10. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/service/core/impl/MessageServiceImpl.kt`**
(Refactored `processNewMessage` methods for distinct streaming/non-streaming DB logic)

```kotlin
package eu.torvian.chatbot.server.service.core.impl
import arrow.core.*
import arrow.core.raise.*
import eu.torvian.chatbot.common.api.ApiError // Import for toApiError()
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.*
import eu.torvian.chatbot.server.service.llm.LLMApiClient
import eu.torvian.chatbot.server.service.llm.LLMCompletionError
import eu.torvian.chatbot.server.service.llm.LLMCompletionResult
import eu.torvian.chatbot.server.service.llm.LLMStreamChunk
import eu.torvian.chatbot.server.service.core.LLMModelService
import eu.torvian.chatbot.server.service.core.LLMProviderService
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.ModelSettingsService
import eu.torvian.chatbot.server.service.core.error.message.*
import eu.torvian.chatbot.server.service.core.error.model.*
import eu.torvian.chatbot.server.service.core.error.provider.*
import eu.torvian.chatbot.server.service.core.error.settings.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Clock
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

/**
 * Implementation of the [MessageService] interface.
 * Orchestrates message persistence, threading, and LLM interaction.
 */
class MessageServiceImpl(
    private val messageDao: MessageDao,
    private val sessionDao: SessionDao,
    private val llmModelService: LLMModelService,
    private val modelSettingsService: ModelSettingsService,
    private val llmProviderService: LLMProviderService,
    private val llmApiClient: LLMApiClient,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : MessageService {
    companion object {
        private val logger: Logger = LogManager.getLogger(MessageServiceImpl::class.java)
    }

    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> {
        return transactionScope.transaction {
            messageDao.getMessagesBySessionId(sessionId)
        }
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<ProcessNewMessageError, List<ChatMessage>> =
        transactionScope.transaction {
            either {
                // 1. Validate session
                val session = withError({ daoError: SessionError.SessionNotFound ->
                    ProcessNewMessageError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.getSessionById(sessionId).bind()
                }

                // 2. Save user message immediately
                val userMessage = withError({ daoError: InsertMessageError ->
                    when (daoError) {
                        is InsertMessageError.SessionNotFound -> throw IllegalStateException("Session $sessionId not found after validation")
                        is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                    }
                }) {
                    messageDao.insertUserMessage(sessionId, content, parentMessageId).bind()
                }
                if (parentMessageId != null) {
                    withError({ daoError: MessageAddChildError ->
                        throw IllegalStateException("Failed to add user message as child to parent: ${daoError.javaClass.simpleName}")
                    }) {
                        messageDao.addChildToMessage(parentMessageId, userMessage.id).bind()
                    }
                }

                // 3. Get model and settings config
                val modelId = session.currentModelId ?: raise(ProcessNewMessageError.ModelConfigurationError("No model selected for session"))
                val settingsId = session.currentSettingsId ?: raise(ProcessNewMessageError.ModelConfigurationError("No settings selected for session"))
                val (model, settings, provider, apiKey) = getLlmConfig(session, modelId, settingsId).bind()

                // 4. Build context
                val allMessagesInSession = messageDao.getMessagesBySessionId(sessionId)
                val context = buildContext(userMessage, allMessagesInSession)

                // 5. Call LLM API (NON-STREAMING)
                val llmResponseResult = withError({ llmError: LLMCompletionError ->
                    logger.error("LLM API call failed for session $sessionId, provider ${provider.name}: $llmError")
                    ProcessNewMessageError.ExternalServiceError(llmError)
                }) {
                    llmApiClient.completeChat( // Call non-streaming method
                        messages = context,
                        modelConfig = model,
                        provider = provider,
                        settings = settings,
                        apiKey = apiKey
                    ).bind()
                }
                logger.info("LLM API call successful for session $sessionId")

                // 6. Process the LLM response and save the assistant message.
                val assistantMessageContent = llmResponseResult.choices.firstOrNull()?.content ?: run {
                    logger.error("LLM API returned successful response with no choices or empty content for session $sessionId")
                    raise(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.InvalidResponseError("LLM API returned success but no completion choices.")))
                }
                val assistantMessage = withError({ daoError: InsertMessageError ->
                    throw IllegalStateException("Failed to insert assistant message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.insertAssistantMessage(
                        sessionId,
                        assistantMessageContent,
                        userMessage.id, // Parent is user message
                        modelId,
                        settingsId
                    ).bind()
                }

                // 7. Add assistant message as child to user message
                withError({ daoError: MessageAddChildError ->
                    throw IllegalStateException("Failed to add assistant message as child to user message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.addChildToMessage(userMessage.id, assistantMessage.id).bind()
                }

                // 8. Update session's leaf message ID
                withError({ daoError: SessionError ->
                    throw IllegalStateException("Failed to update session leaf message ID: ${daoError.javaClass.simpleName}")
                }) {
                    sessionDao.updateSessionLeafMessageId(sessionId, assistantMessage.id).bind()
                }

                // 9. Return new messages
                listOf(userMessage, assistantMessage)
            }
        }

    override fun processNewMessageStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Flow<Either<ProcessNewMessageError, ChatUpdate>> = channelFlow { // Use channelFlow for easier control of stream
        // 1. Save user message immediately in its own transaction block.
        // This ensures the user message is persisted even if streaming fails later.
        val userMessage = transactionScope.transaction {
            either {
                val session = sessionDao.getSessionById(sessionId).getOrElse { raise(ProcessNewMessageError.SessionNotFound(sessionId)) }
                val userMsg = messageDao.insertUserMessage(sessionId, content, parentMessageId).bind { daoError ->
                    when (daoError) {
                        is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                        is InsertMessageError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.sessionId)
                    }
                }
                if (parentMessageId != null) {
                    messageDao.addChildToMessage(parentMessageId, userMsg.id).bind {
                        // Log a warning, but don't fail the whole operation for a linking issue
                        logger.warn("Failed to link user message ${userMsg.id} as child to parent ${parentMessageId}: ${it.message}")
                        ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link user message.").left()
                    }
                }
                userMsg
            }
        }.fold(
            ifLeft = { error ->
                logger.error("Initial user message save failed for session $sessionId: $error")
                send(error.left()) // Send error to client and terminate flow
                close()
                return@channelFlow
            },
            ifRight = { it }
        )

        // 2. Emit the successfully saved user message as the first update
        send(ChatUpdate.UserMessageUpdate(userMessage).right())

        // 3. Get LLM model, settings, provider, and API key
        val (model, settings, provider, apiKey) = either {
            val session = sessionDao.getSessionById(sessionId).getOrElse { raise(ProcessNewMessageError.SessionNotFound(sessionId)) }
            getLlmConfig(session, session.currentModelId ?: -1, session.currentSettingsId ?: -1).bind()
        }.getOrElse { error ->
            logger.error("LLM configuration retrieval failed for session $sessionId: $error")
            send(error.left()) // Send error to client and terminate flow
            close()
            return@channelFlow
        }

        // 4. Build context for LLM call (includes the newly saved user message)
        val allMessagesInSession = transactionScope.transaction { messageDao.getMessagesBySessionId(sessionId) }
        val context = buildContext(userMessage, allMessagesInSession)

        // Generate a temporary ID for the assistant message on the server-side,
        // which will be passed to the client to track the streaming message.
        // The client will then replace this temporary ID with the real DB ID at the end.
        val temporaryAssistantMessageId = -1L // A common convention for "not-yet-persisted" or client-only ID

        // 5. Emit AssistantMessageStart with temporary ID to client
        send(ChatUpdate.AssistantMessageStart(
            messageId = temporaryAssistantMessageId,
            parentId = userMessage.id,
            sessionId = sessionId,
            createdAt = Clock.System.now(), // Client can use this for display
            modelId = model.id,
            settingsId = settings.id
        ).right())

        var accumulatedContent = ""
        var finalUsage: LLMCompletionResult.UsageStats = LLMCompletionResult.UsageStats(0, 0, 0)
        var finalFinishReason: String? = null

        try {
            // 6. Call LLM API in streaming mode and collect chunks
            llmApiClient.completeChatStreaming(context, model, provider, settings, apiKey).collect { llmStreamChunkEither ->
                llmStreamChunkEither.fold(
                    ifLeft = { llmError ->
                        logger.error("LLM API streaming error for session $sessionId, provider ${provider.name}: $llmError")
                        // If an error occurs in the stream, emit it and signal termination
                        send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(llmError).toApiError()).right())
                        throw ProcessNewMessageError.ExternalServiceError(llmError) // Propagate error to cause catch block
                    },
                    ifRight = { chunk ->
                        when (chunk) {
                            is LLMStreamChunk.ContentChunk -> {
                                accumulatedContent += chunk.deltaContent
                                finalFinishReason = chunk.finishReason ?: finalFinishReason // Update finish reason if provided mid-stream
                                send(ChatUpdate.AssistantMessageDelta(temporaryAssistantMessageId, chunk.deltaContent).right())
                            }
                            is LLMStreamChunk.UsageChunk -> {
                                finalUsage = LLMCompletionResult.UsageStats(chunk.promptTokens, chunk.completionTokens, chunk.totalTokens)
                            }
                            LLMStreamChunk.Done -> {
                                // 7. Stream is finished and successful. NOW save the assistant message to the DB.
                                val savedAssistantMessage = transactionScope.transaction {
                                    either {
                                        val assistantMsg = messageDao.insertAssistantMessage(
                                            sessionId,
                                            accumulatedContent,
                                            userMessage.id, // Parent is user message
                                            model.id,
                                            settings.id
                                        ).bind { daoError ->
                                            throw IllegalStateException("Failed to insert final assistant message: ${daoError.javaClass.simpleName}")
                                        }
                                        // Link assistant message as child to user message in DB
                                        messageDao.addChildToMessage(userMessage.id, assistantMsg.id).bind {
                                            logger.warn("Failed to link assistant message ${assistantMsg.id} as child to user message ${userMessage.id}: ${it.message}")
                                            ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link assistant message.").left()
                                        }
                                        // Update session's leaf message ID to the newly saved assistant message
                                        sessionDao.updateSessionLeafMessageId(sessionId, assistantMsg.id).bind {
                                            logger.warn("Failed to update session leaf message ID to ${assistantMsg.id}: ${it.message}")
                                            ProcessNewMessageError.ModelConfigurationError("Internal error: failed to update session leaf.").left()
                                        }
                                        assistantMsg
                                    }
                                }.getOrElse { error ->
                                    logger.error("Final assistant message save failed for session $sessionId: $error")
                                    send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.OtherError("Failed to save final message.")).toApiError()).right())
                                    throw ProcessNewMessageError.ExternalServiceError(LLMCompletionError.OtherError("Failed to save final message.")) // Propagate error to outer catch
                                }

                                // 8. Emit the final update with the REAL database ID
                                send(ChatUpdate.AssistantMessageEnd(
                                    tempMessageId = temporaryAssistantMessageId,
                                    finalMessage = savedAssistantMessage as ChatMessage.AssistantMessage
                                ).right())
                                send(ChatUpdate.Done.right()) // Signal flow completion
                            }
                            is LLMStreamChunk.Error -> {
                                logger.error("LLM API returned streaming error chunk: ${chunk.llmError}")
                                send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(chunk.llmError).toApiError()).right())
                                throw ProcessNewMessageError.ExternalServiceError(chunk.llmError) // Propagate error
                            }
                        }
                    }
                )
            }
        } catch (e: Exception) {
            // This catches any errors that were `throw`n (including those from LLM API stream)
            logger.error("Streaming message processing failed for session $sessionId due to unhandled exception: ${e.message}", e)
            if (e !is ProcessNewMessageError) { // Ensure we only emit if it's not already a mapped error
                send(ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.OtherError("An unexpected error occurred during streaming.", e)).toApiError()).right())
            }
        } finally {
            close() // Ensure the channel is closed after completion or error
        }
    }

    override suspend fun updateMessageContent(
        id: Long,
        content: String
    ): Either<UpdateMessageContentError, ChatMessage> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    UpdateMessageContentError.MessageNotFound(daoError.id)
                }) {
                    messageDao.updateMessageContent(id, content).bind()
                }
            }
        }

    override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    DeleteMessageError.MessageNotFound(daoError.id)
                }) {
                    messageDao.deleteMessage(id).bind()
                }
            }
        }

    private suspend fun Raise<ProcessNewMessageError>.getLlmConfig(
        session: eu.torvian.chatbot.common.models.ChatSession,
        modelId: Long,
        settingsId: Long
    ): Triple<eu.torvian.chatbot.common.models.LLMModel, eu.torvian.chatbot.common.models.ModelSettings, eu.torvian.chatbot.common.models.LLMProvider, String?> {
        val model = withError({ serviceError: GetModelError ->
            ProcessNewMessageError.ModelConfigurationError("Model with ID $modelId not found: ${serviceError.message}")
        }) {
            llmModelService.getModelById(modelId).bind()
        }
        val settings = withError({ serviceError: GetSettingsByIdError ->
            ProcessNewMessageError.ModelConfigurationError("Settings with ID $settingsId not found: ${serviceError.message}")
        }) {
            modelSettingsService.getSettingsById(settingsId).bind()
        }
        val provider = withError({ serviceError: GetProviderError ->
            ProcessNewMessageError.ModelConfigurationError("Provider not found for model ID $modelId (provider ID: ${model.providerId}): ${serviceError.message}")
        }) {
            llmProviderService.getProviderById(model.providerId).bind()
        }
        val apiKey = provider.apiKeyId?.let { keyId ->
            withError({ credError: CredentialError.CredentialNotFound ->
                ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
            }) {
                credentialManager.getCredential(keyId).bind()
            }
        }
        return Triple(model, settings, provider, apiKey)
    }

    /**
     * Builds the context for the LLM API call.
     *
     * @param currentUserMessage The user's message.
     * @param allMessages All messages in the session.
     * @return The context as a list of [ChatMessage] objects.
     */
    private fun buildContext(currentUserMessage: ChatMessage, allMessages: List<ChatMessage>): List<ChatMessage> {
        val context = mutableListOf<ChatMessage>()
        val messageMap = allMessages.associateBy { it.id }
        var c: ChatMessage? = currentUserMessage
        while (c != null) {
            context.add(0, c)
            c = c.parentMessageId?.let { messageMap[it] }
        }
        return context
    }
}
```

**11. `chatbot/server/src/main/kotlin/eu/torvian/chatbot/server/ktor/routes/configureSessionRoutes.kt`**
(Logic to switch between streaming/non-streaming based on `request.stream`)

```kotlin
package eu.torvian.chatbot.server.ktor.routes
import eu.torvian.chatbot.common.api.ChatbotApiErrorCodes
import eu.torvian.chatbot.common.api.CommonApiErrorCodes
import eu.torvian.chatbot.common.api.apiError
import eu.torvian.chatbot.common.api.resources.SessionResource
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.SessionService
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.session.*
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import io.ktor.server.sse.* // Import SSE
import kotlinx.serialization.json.Json // Assuming Json is available or injected
import org.koin.ktor.ext.inject // If using Koin, to inject Json

/**
 * Configures routes related to Sessions (/api/v1/sessions) using Ktor Resources.
 */
fun Route.configureSessionRoutes(
    sessionService: SessionService,
    messageService: MessageService
) {
    val json: Json by inject() // Inject the Json instance for serialization

    // GET /api/v1/sessions - List all sessions
    get<SessionResource> {
        call.respond(sessionService.getAllSessionsSummaries())
    }

    // POST /api/v1/sessions - Create a new session
    post<SessionResource> {
        val request = call.receive<CreateSessionRequest>()
        call.respondEither(
            sessionService.createSession(request.name),
            HttpStatusCode.Created
        ) { error ->
            when (error) {
                is CreateSessionError.InvalidName ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid session name provided",
                        "reason" to error.reason
                    )
                is CreateSessionError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid related entity ID provided",
                        "details" to error.message
                    )
            }
        }
    }

    // GET /api/v1/sessions/{sessionId} - Get session by ID
    get<SessionResource.ById> { resource ->
        val sessionId = resource.sessionId
        call.respondEither(sessionService.getSessionDetails(sessionId)) { error ->
            when (error) {
                is GetSessionDetailsError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
            }
        }
    }

    // DELETE /api/v1/sessions/{sessionId} - Delete session by ID
    delete<SessionResource.ById> { resource ->
        val sessionId = resource.sessionId
        call.respondEither(
            sessionService.deleteSession(sessionId),
            HttpStatusCode.NoContent
        ) { error ->
            when (error) {
                is DeleteSessionError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
            }
        }
    }

    // --- Granular PUT routes using nested resources ---
    // PUT /api/v1/sessions/{sessionId}/name - Update the name of a session
    put<SessionResource.ById.Name> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionNameRequest>()
        call.respondEither(sessionService.updateSessionName(sessionId, request.name)) { error ->
            when (error) {
                is UpdateSessionNameError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionNameError.InvalidName ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid session name provided",
                        "reason" to error.reason
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/model - Update the current model ID of a session
    put<SessionResource.ById.Model> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionModelRequest>()
        call.respondEither(
            sessionService.updateSessionCurrentModelId(sessionId, request.modelId)
        ) { error ->
            when (error) {
                is UpdateSessionCurrentModelIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionCurrentModelIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid model ID provided",
                        "modelId" to request.modelId.toString()
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/settings - Update the current settings ID of a session
    put<SessionResource.ById.Settings> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionSettingsRequest>()
        call.respondEither(
            sessionService.updateSessionCurrentSettingsId(sessionId, request.settingsId)
        ) { error ->
            when (error) {
                is UpdateSessionCurrentSettingsIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionCurrentSettingsIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid settings ID provided",
                        "settingsId" to request.settingsId.toString()
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/leafMessage - Update the current leaf message ID of a session
    put<SessionResource.ById.LeafMessage> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionLeafMessageRequest>()
        call.respondEither(
            sessionService.updateSessionLeafMessageId(sessionId, request.leafMessageId)
        ) { error ->
            when (error) {
                is UpdateSessionLeafMessageIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionLeafMessageIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid leaf message ID provided",
                        "leafMessageId" to request.leafMessageId.toString()
                    )
            }
        }
    }

    // PUT /api/v1/sessions/{sessionId}/group - Assign session to group or ungroup
    put<SessionResource.ById.Group> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<UpdateSessionGroupRequest>()
        call.respondEither(
            sessionService.updateSessionGroupId(sessionId, request.groupId)
        ) { error ->
            when (error) {
                is UpdateSessionGroupIdError.SessionNotFound ->
                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.id.toString())
                is UpdateSessionGroupIdError.InvalidRelatedEntity ->
                    apiError(
                        CommonApiErrorCodes.INVALID_ARGUMENT,
                        "Invalid group ID provided",
                        "groupId" to request.groupId.toString()
                    )
            }
        }
    }

    // POST /api/v1/sessions/{sessionId}/messages - Process a new message for a session
    post<SessionResource.ById.Messages> { resource ->
        val sessionId = resource.parent.sessionId
        val request = call.receive<ProcessNewMessageRequest>()

        if (request.stream) {
            // Handle Streaming Request
            call.respondSse(eventProducer = {
                messageService.processNewMessageStreaming(
                    sessionId,
                    request.content,
                    request.parentMessageId
                ).collect { eitherUpdate ->
                    eitherUpdate.fold(
                        ifLeft = { error ->
                            val apiError = when (error) {
                                is ProcessNewMessageError.SessionNotFound ->
                                    apiError(CommonApiErrorCodes.NOT_FOUND, "Session not found", "sessionId" to error.sessionId.toString())
                                is ProcessNewMessageError.ParentNotInSession ->
                                    apiError(CommonApiErrorCodes.INVALID_STATE, "Parent message does not belong to this session", "sessionId" to error.sessionId.toString(), "parentId" to error.parentId.toString())
                                is ProcessNewMessageError.ModelConfigurationError ->
                                    apiError(ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR, "LLM configuration error", "details" to error.message)
                                is ProcessNewMessageError.ExternalServiceError ->
                                    apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
                            }
                            // Send an error event to the client
                            send(SseEvent(data = json.encodeToString(ChatUpdate.ErrorUpdate(apiError)), event = "error"))
                            // Close the stream on server-side error
                            close()
                        },
                        ifRight = { chatUpdate ->
                            // Determine the SSE event type based on ChatUpdate sealed class member
                            val eventType = when (chatUpdate) {
                                is ChatUpdate.UserMessageUpdate -> "user_message"
                                is ChatUpdate.AssistantMessageStart -> "assistant_message_start"
                                is ChatUpdate.AssistantMessageDelta -> "assistant_message_delta"
                                is ChatUpdate.AssistantMessageEnd -> "assistant_message_end"
                                is ChatUpdate.ErrorUpdate -> "error" // Redundant if left branch handles, but safe
                                ChatUpdate.Done -> "done"
                            }
                            // Send the ChatUpdate object serialized as JSON
                            send(SseEvent(data = json.encodeToString(chatUpdate), event = eventType))
                            if (chatUpdate == ChatUpdate.Done) {
                                close() // Ensure the SSE connection is closed after sending DONE
                            }
                        }
                    )
                }
            })
        } else {
            // Handle Non-Streaming Request
            call.respondEither(
                messageService.processNewMessage(
                    sessionId,
                    request.content,
                    request.parentMessageId
                ), HttpStatusCode.Created
            ) { error ->
                when (error) {
                    is ProcessNewMessageError.SessionNotFound ->
                        apiError(
                            CommonApiErrorCodes.NOT_FOUND,
                            "Session not found",
                            "sessionId" to error.sessionId.toString()
                        )
                    is ProcessNewMessageError.ParentNotInSession ->
                        apiError(
                            CommonApiErrorCodes.INVALID_STATE,
                            "Parent message does not belong to this session",
                            "sessionId" to error.sessionId.toString(),
                            "parentId" to error.parentId.toString()
                        )
                    is ProcessNewMessageError.ModelConfigurationError ->
                        apiError(
                            ChatbotApiErrorCodes.MODEL_CONFIGURATION_ERROR,
                            "LLM configuration error",
                            "details" to error.message
                        )
                    is ProcessNewMessageError.ExternalServiceError ->
                        apiError(ChatbotApiErrorCodes.EXTERNAL_SERVICE_ERROR, "LLM API Error", "details" to error.llmError.toString())
                }
            }
        }
    }
}
```
