package eu.torvian.chatbot.app.service.api.ktor

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.api.resources.MessageResource
import eu.torvian.chatbot.common.api.resources.SessionResource
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatUpdate
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.utils.io.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json

/**
 * Ktor HttpClient implementation of the [ChatApi] interface.
 *
 * Uses the configured [HttpClient] and the [BaseApiClient.safeApiCall] helper
 * to interact with the backend's chat message endpoints, mapping responses
 * to [Either<ApiError, T>].
 *
 * @property client The Ktor HttpClient instance injected for making requests.
 */
class KtorChatApiClient(client: HttpClient) : BaseApiClient(client), ChatApi {
    companion object {
        private val logger = kmpLogger<KtorChatApiClient>()
    }

    // Need a Json instance for deserialization.
    // Ensure this matches the configuration of the Ktor client's ContentNegotiation.
    // It's best practice to inject this if your DI framework supports it.
    private val json: Json = Json {
        ignoreUnknownKeys = true
        isLenient = true
        encodeDefaults = true
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        request: ProcessNewMessageRequest
    ): Either<ApiError, List<ChatMessage>> {
        // Use safeApiCall to wrap the Ktor request
        return safeApiCall {
            // Use Ktor resources to build the URL: /api/v1/sessions/{sessionId}/messages
            client.post(SessionResource.ById.Messages(SessionResource.ById(SessionResource(), sessionId))) {
                // Set the request body with the ProcessNewMessageRequest DTO
                setBody(request)
                timeout {
                    requestTimeoutMillis = HttpTimeoutConfig.INFINITE_TIMEOUT_MS // Allow indefinite stream
                }
            }.body<List<ChatMessage>>() // Expect a List<ChatMessage> in the response body on success (HTTP 201)
        }
    }

    override fun processNewMessageStreaming(
        sessionId: Long,
        request: ProcessNewMessageRequest
    ): Flow<Either<ApiError, ChatUpdate>> = flow {
        try {
            // Use preparePost and execute to get a streaming response
            client.preparePost(SessionResource.ById.Messages(SessionResource.ById(SessionResource(), sessionId))) {
                setBody(request)
                // Explicitly request SSE content type if not already default
                accept(ContentType.Text.EventStream)
                timeout {
                    requestTimeoutMillis = HttpTimeoutConfig.INFINITE_TIMEOUT_MS // Allow indefinite stream
                }
            }.execute { httpResponse ->
                if (httpResponse.status.isSuccess()) {
                    val channel: ByteReadChannel = httpResponse.bodyAsChannel()
                    while (!channel.isClosedForRead) {
                        val line = channel.readUTF8Line()
                        if (line == null || line.isBlank()) continue // Skip empty lines
                        if (line.startsWith("data:")) {
                            val data = line.removePrefix("data:").trim()
                            if (data.isNotBlank()) {
                                try {
                                    val chatUpdate = json.decodeFromString<ChatUpdate>(data)
                                    emit(chatUpdate.right())
                                    if (chatUpdate is ChatUpdate.Done || chatUpdate is ChatUpdate.ErrorUpdate) {
                                        // Explicitly stop collecting if done or error event received
                                        break
                                    }
                                } catch (e: Exception) {
                                    logger.error("Failed to parse SSE data chunk for session $sessionId: '$data'", e)
                                    emit(
                                        ApiError(
                                            statusCode = 500,
                                            code = "parsing-error",
                                            message = "Failed to parse chat update chunk",
                                            details = mapOf("error" to (e.message ?: "Unknown parsing error"))
                                        ).left()
                                    )
                                    break // Stop on parsing error
                                }
                            }
                        } else if (line.startsWith("event:")) {
                            // You can read the event type if needed, e.g., val eventType = line.removePrefix("event:").trim()
                        }
                        // Ignore other SSE fields like "id:", "retry:", etc.
                    }
                } else {
                    // Handle non-2xx HTTP status codes immediately
                    val errorBody = httpResponse.bodyAsText()
                    val apiError = try {
                        json.decodeFromString<ApiError>(errorBody)
                    } catch (e: Exception) {
                        logger.warn("Failed to parse error response body, creating generic error", e)
                        ApiError(
                            statusCode = httpResponse.status.value,
                            code = "unknown-error",
                            message = "HTTP ${httpResponse.status.value}: ${httpResponse.status.description}",
                            details = mapOf("originalBody" to errorBody.take(200))
                        )
                    }
                    logger.error("API call for session $sessionId failed with status ${httpResponse.status.value}: $apiError")
                    emit(apiError.left())
                }
            }
        } catch (e: Exception) {
            logger.error("Network or streaming error during processNewMessageStreaming for session $sessionId", e)
            emit(
                ApiError(
                    statusCode = 500,
                    code = "network-error",
                    message = "Network or communication error during streaming: ${e.message}",
                    details = mapOf("error" to (e.message ?: "Unknown network error"))
                ).left()
            )
        }
    }

    override suspend fun updateMessageContent(
        messageId: Long,
        request: UpdateMessageRequest
    ): Either<ApiError, ChatMessage> {
        // Use safeApiCall to wrap the Ktor request
        return safeApiCall {
            // Use Ktor resources to build the URL: /api/v1/messages/{messageId}/content
            client.put(MessageResource.ById.Content(MessageResource.ById(MessageResource(), messageId))) {
                // Set the request body with the UpdateMessageRequest DTO
                setBody(request)
            }.body<ChatMessage>() // Expect a ChatMessage in the response body on success (HTTP 200)
        }
    }

    override suspend fun deleteMessage(messageId: Long): Either<ApiError, Unit> {
        // Use safeApiCall to wrap the Ktor request
        return safeApiCall {
            // Use Ktor resources to build the URL: /api/v1/messages/{messageId}
            client.delete(MessageResource.ById(MessageResource(), messageId))
                // The backend should return HTTP 204 No Content on success.
                // Ktor's body<Unit>() can be used, or simply letting the request complete
                // indicates success for Unit return type in safeApiCall.
                .body<Unit>() // Explicitly expect Unit body on success
        }
    }
}