package eu.torvian.chatbot.server.service.core.impl

import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.raise.either
import arrow.core.raise.withError
import arrow.core.right
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.InsertMessageError
import eu.torvian.chatbot.server.data.dao.error.MessageAddChildError
import eu.torvian.chatbot.server.data.dao.error.MessageError
import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.service.core.LLMModelService
import eu.torvian.chatbot.server.service.core.LLMProviderService
import eu.torvian.chatbot.server.service.core.MessageService
import eu.torvian.chatbot.server.service.core.ModelSettingsService
import eu.torvian.chatbot.server.service.core.error.message.DeleteMessageError
import eu.torvian.chatbot.server.service.core.error.message.ProcessNewMessageError
import eu.torvian.chatbot.server.service.core.error.message.UpdateMessageContentError
import eu.torvian.chatbot.server.service.core.error.message.toApiError
import eu.torvian.chatbot.server.service.core.error.model.GetModelError
import eu.torvian.chatbot.server.service.core.error.provider.GetProviderError
import eu.torvian.chatbot.server.service.core.error.settings.GetSettingsByIdError
import eu.torvian.chatbot.server.service.llm.LLMApiClient
import eu.torvian.chatbot.server.service.llm.LLMCompletionError
import eu.torvian.chatbot.server.service.llm.LLMStreamChunk
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.datetime.Clock
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger


/**
 * Implementation of the [MessageService] interface.
 * Orchestrates message persistence, threading, and LLM interaction.
 */
class MessageServiceImpl(
    private val messageDao: MessageDao,
    private val sessionDao: SessionDao,
    private val llmModelService: LLMModelService,
    private val modelSettingsService: ModelSettingsService,
    private val llmProviderService: LLMProviderService,
    private val llmApiClient: LLMApiClient,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : MessageService {

    companion object {
        private val logger: Logger = LogManager.getLogger(MessageServiceImpl::class.java)
    }

    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> {
        return transactionScope.transaction {
            messageDao.getMessagesBySessionId(sessionId)
        }
    }

    override suspend fun processNewMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<ProcessNewMessageError, List<ChatMessage>> =
        transactionScope.transaction {
            either {

                // 1. Validate session
                val session = withError({ daoError: SessionError.SessionNotFound ->
                    ProcessNewMessageError.SessionNotFound(daoError.id)
                }) {
                    sessionDao.getSessionById(sessionId).bind()
                }

                // 2. Save user message
                val userMessage = withError({ daoError: InsertMessageError ->
                    when (daoError) {
                        is InsertMessageError.SessionNotFound -> {
                            throw IllegalStateException("Session $sessionId not found after validation")
                        }

                        is InsertMessageError.ParentNotInSession -> {
                            ProcessNewMessageError.ParentNotInSession(daoError.sessionId, daoError.parentId)
                        }
                    }
                }) {
                    messageDao.insertUserMessage(sessionId, content, parentMessageId).bind()
                }

                // 3. Add user message as child to parent
                if (parentMessageId != null) {
                    withError({ daoError: MessageAddChildError ->
                        throw IllegalStateException("Failed to add user message as child to parent: ${daoError.javaClass.simpleName}")
                    }) {
                        messageDao.addChildToMessage(parentMessageId, userMessage.id).bind()
                    }
                }

                // 4. Get model and settings config
                val modelId = session.currentModelId
                    ?: raise(ProcessNewMessageError.ModelConfigurationError("No model selected for session"))
                val settingsId = session.currentSettingsId
                    ?: raise(ProcessNewMessageError.ModelConfigurationError("No settings selected for session"))

                // Fetch Model
                val model = withError({ serviceError: GetModelError ->
                    throw IllegalStateException("Model with ID $modelId not found after validation")
                }) {
                    llmModelService.getModelById(modelId).bind()
                }

                // Fetch Settings
                val settings = withError({ serviceError: GetSettingsByIdError ->
                    when (serviceError) {
                        is GetSettingsByIdError.SettingsNotFound -> {
                            throw IllegalStateException("Settings with ID $settingsId not found after validation")
                        }
                    }
                }) {
                    modelSettingsService.getSettingsById(settingsId).bind()
                }

                // Get provider and API Key from the model's provider (if the provider requires one)
                val (provider, apiKey) = run {
                    // First get the provider for this model
                    val provider = withError({ serviceError: GetProviderError ->
                        throw IllegalStateException("Provider not found for model ID $modelId (provider ID: ${model.providerId})")
                    }) {
                        llmProviderService.getProviderById(model.providerId).bind()
                    }

                    // Then get the credential using the provider's API key ID (if it has one)
                    val apiKey = provider.apiKeyId?.let { keyId ->
                        withError({ credError: CredentialError.CredentialNotFound ->
                            throw IllegalStateException("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
                        }) {
                            credentialManager.getCredential(keyId).bind()
                        }
                    }

                    provider to apiKey
                }

                // 5. Build context
                val allMessagesInSession = messageDao.getMessagesBySessionId(sessionId)
                val context = buildContext(userMessage, allMessagesInSession)

                // 6. Call LLM API
                val llmResponseResult = withError({ llmError: LLMCompletionError ->
                    logger.error("LLM API call failed for session $sessionId, provider ${provider.name}: $llmError")
                    ProcessNewMessageError.ExternalServiceError(llmError)
                }) {
                    llmApiClient.completeChat(
                        messages = context,
                        modelConfig = model,
                        provider = provider,
                        settings = settings,
                        apiKey = apiKey
                    ).bind()
                }
                logger.info("LLM API call successful for session $sessionId")

                // 7. Process the LLM response and save the assistant message.
                // Extract content from the first completion choice.
                val assistantMessageContent =
                    llmResponseResult.choices.firstOrNull()?.content ?: run {
                        logger.error("LLM API returned successful response with no choices or empty content for session $sessionId")
                        // Treat an empty response as an error scenario
                        raise(ProcessNewMessageError.ExternalServiceError(LLMCompletionError.InvalidResponseError("LLM API returned success but no completion choices.")))
                    }
                val assistantMessage = withError({ daoError: InsertMessageError ->
                    throw IllegalStateException("Failed to insert assistant message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.insertAssistantMessage(
                        sessionId,
                        assistantMessageContent,
                        userMessage.id,
                        modelId,
                        settingsId
                    ).bind()
                }

                // 8. Add assistant message as child to user message
                withError({ daoError: MessageAddChildError ->
                    throw IllegalStateException("Failed to add assistant message as child to user message: ${daoError.javaClass.simpleName}")
                }) {
                    messageDao.addChildToMessage(userMessage.id, assistantMessage.id).bind()
                }

                // 9. Update session's leaf message ID
                withError({ daoError: SessionError ->
                    throw IllegalStateException("Failed to update session leaf message ID: ${daoError.javaClass.simpleName}")
                }) {
                    sessionDao.updateSessionLeafMessageId(sessionId, assistantMessage.id).bind()
                }

                // 10. Return new messages as the success value
                listOf(userMessage, assistantMessage)
            }
        }

    override fun processNewMessageStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Flow<Either<ProcessNewMessageError, ChatUpdate>> = channelFlow {

        // Step 1: Save user message
        val userMessage = saveUserMessageForStreaming(sessionId, content, parentMessageId)
            .getOrElse { error ->
                logger.error("Initial user message save failed for session $sessionId: $error")
                send(error.left())
                close()
                return@channelFlow
            }


        send(ChatUpdate.UserMessageUpdate(userMessage).right())

        // Step 2: Get LLM configuration
        val llmConfig = getLlmConfigForStreaming(sessionId)
            .getOrElse { error ->
                logger.error("LLM configuration retrieval failed for session $sessionId: $error")
                send(error.left())
                close()
                return@channelFlow
            }

        // Step 3: Build context and create temporary assistant message
        val context = buildStreamingContext(sessionId, userMessage)
        val temporaryAssistantMessage = createTemporaryAssistantMessage(
            sessionId, userMessage, llmConfig.model, llmConfig.settings
        )

        send(ChatUpdate.AssistantMessageStart(temporaryAssistantMessage).right())

        // Step 4: Handle streaming with callbacks
        try {
            handleLlmStreaming(
                context = context,
                model = llmConfig.model,
                provider = llmConfig.provider,
                settings = llmConfig.settings,
                apiKey = llmConfig.apiKey,
                onContentDelta = { deltaContent ->
                    send(ChatUpdate.AssistantMessageDelta(temporaryAssistantMessage.id, deltaContent).right())
                },
                onStreamComplete = { finalContent ->
                    val (savedAssistantMessage, updatedUserMessage) = saveFinalAssistantMessage(
                        sessionId, finalContent, userMessage, llmConfig.model, llmConfig.settings
                    ).getOrElse { error ->
                        logger.error("Final assistant message save failed for session $sessionId: $error")
                        send(
                            ChatUpdate.ErrorUpdate(
                                ProcessNewMessageError.ExternalServiceError(
                                    LLMCompletionError.OtherError("Failed to save final message.")
                                ).toApiError()
                            ).right()
                        )
                        throw RuntimeException("Failed to save final message: $error")
                    }

                    send(
                        ChatUpdate.AssistantMessageEnd(
                            tempMessageId = temporaryAssistantMessage.id,
                            finalAssistantMessage = savedAssistantMessage,
                            finalUserMessage = updatedUserMessage
                        ).right()
                    )

                    send(ChatUpdate.Done.right())
                },
                onError = { llmError ->
                    send(
                        ChatUpdate.ErrorUpdate(ProcessNewMessageError.ExternalServiceError(llmError).toApiError())
                            .right()
                    )
                    throw RuntimeException("LLM streaming error: $llmError")
                }
            )
        } catch (e: Exception) {
            logger.error("Streaming message processing failed for session $sessionId: ${e.message}", e)
            send(
                ChatUpdate.ErrorUpdate(
                    ProcessNewMessageError.ExternalServiceError(
                        LLMCompletionError.OtherError("An unexpected error occurred during streaming.", e)
                    ).toApiError()
                ).right()
            )
        } finally {
            close()
        }
    }


    override suspend fun updateMessageContent(
        id: Long,
        content: String
    ): Either<UpdateMessageContentError, ChatMessage> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    UpdateMessageContentError.MessageNotFound(daoError.id)
                }) {
                    messageDao.updateMessageContent(id, content).bind()
                }
            }
        }

    override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: MessageError.MessageNotFound ->
                    DeleteMessageError.MessageNotFound(daoError.id)
                }) {
                    messageDao.deleteMessage(id).bind()
                }
            }
        }

    /**
     * Builds the context for the LLM API call.
     *
     * @param currentUserMessage The user's message.
     * @param allMessages All messages in the session.
     * @return The context as a list of [ChatMessage] objects.
     */
    private fun buildContext(currentUserMessage: ChatMessage, allMessages: List<ChatMessage>): List<ChatMessage> {
        val context = mutableListOf<ChatMessage>()
        val messageMap = allMessages.associateBy { it.id }
        var c: ChatMessage? = currentUserMessage
        while (c != null) {
            context.add(0, c)
            c = c.parentMessageId?.let { messageMap[it] }
        }
        return context
    }

    /**
     * Saves user message and handles parent linking in a transaction.
     */
    private suspend fun saveUserMessageForStreaming(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<ProcessNewMessageError, ChatMessage.UserMessage> = transactionScope.transaction {
        either {
            val userMsg = messageDao.insertUserMessage(sessionId, content, parentMessageId).mapLeft { daoError ->
                when (daoError) {
                    is InsertMessageError.ParentNotInSession -> ProcessNewMessageError.ParentNotInSession(
                        daoError.sessionId, daoError.parentId
                    )

                    is InsertMessageError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.sessionId)
                }
            }.bind()

            if (parentMessageId != null) {
                messageDao.addChildToMessage(parentMessageId, userMsg.id).mapLeft { linkError ->
                    logger.warn("Failed to link user message ${userMsg.id} as child to parent ${parentMessageId}: ${linkError::class.simpleName}")
                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link user message.")
                }.bind()
            }

            userMsg
        }
    }

    /**
     * Creates temporary assistant message for streaming.
     */
    private fun createTemporaryAssistantMessage(
        sessionId: Long,
        userMessage: ChatMessage.UserMessage,
        model: LLMModel,
        settings: ModelSettings
    ): ChatMessage.AssistantMessage {
        return ChatMessage.AssistantMessage(
            id = -1L, // Temporary ID
            sessionId = sessionId,
            content = "",
            parentMessageId = userMessage.id,
            childrenMessageIds = emptyList(),
            createdAt = Clock.System.now(),
            updatedAt = Clock.System.now(),
            modelId = model.id,
            settingsId = settings.id
        )
    }

    /**
     * Saves final assistant message and updates relationships.
     */
    private suspend fun saveFinalAssistantMessage(
        sessionId: Long,
        content: String,
        userMessage: ChatMessage.UserMessage,
        model: LLMModel,
        settings: ModelSettings
    ): Either<ProcessNewMessageError, Pair<ChatMessage.AssistantMessage, ChatMessage.UserMessage>> =
        transactionScope.transaction {
            either {
                val assistantMsg = messageDao.insertAssistantMessage(
                    sessionId, content, userMessage.id, model.id, settings.id
                ).mapLeft { daoError ->
                    ProcessNewMessageError.ModelConfigurationError("Failed to insert final assistant message: ${daoError::class.simpleName}")
                }.bind()

                messageDao.addChildToMessage(userMessage.id, assistantMsg.id).mapLeft { linkError ->
                    logger.warn("Failed to link assistant message ${assistantMsg.id} as child to user message ${userMessage.id}: ${linkError::class.simpleName}")
                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to link assistant message.")
                }.bind()

                sessionDao.updateSessionLeafMessageId(sessionId, assistantMsg.id).mapLeft { updateError ->
                    logger.warn("Failed to update session leaf message ID to ${assistantMsg.id}: ${updateError::class.simpleName}")
                    ProcessNewMessageError.ModelConfigurationError("Internal error: failed to update session leaf.")
                }.bind()

                val updatedUserMsg = messageDao.getMessageById(userMessage.id).mapLeft { daoError ->
                    ProcessNewMessageError.ModelConfigurationError("Failed to retrieve updated user message: ${daoError::class.simpleName}")
                }.bind() as ChatMessage.UserMessage

                assistantMsg to updatedUserMsg
            }
        }

    private suspend fun getLlmConfigForStreaming(sessionId: Long): Either<ProcessNewMessageError, LlmConfig> =
        transactionScope.transaction {
            val session = sessionDao.getSessionById(sessionId).getOrElse {
                return@transaction ProcessNewMessageError.SessionNotFound(sessionId).left()
            }
            getLlmConfig(session.currentModelId ?: -1, session.currentSettingsId ?: -1)
        }

    private suspend fun buildStreamingContext(
        sessionId: Long,
        userMessage: ChatMessage.UserMessage
    ): List<ChatMessage> =
        transactionScope.transaction {
            val allMessages = messageDao.getMessagesBySessionId(sessionId)
            buildContext(userMessage, allMessages)
        }

    /**
     * Handles LLM streaming response and emits updates.
     */
    private suspend fun handleLlmStreaming(
        context: List<ChatMessage>,
        model: LLMModel,
        provider: LLMProvider,
        settings: ModelSettings,
        apiKey: String?,
        onContentDelta: suspend (String) -> Unit,
        onStreamComplete: suspend (String) -> Unit,
        onError: suspend (LLMCompletionError) -> Unit
    ) {
        var accumulatedContent = ""

        llmApiClient.completeChatStreaming(context, model, provider, settings, apiKey)
            .collect { llmStreamChunkEither ->
                llmStreamChunkEither.fold(
                    ifLeft = { llmError ->
                        logger.error("LLM API streaming error, provider ${provider.name}: $llmError")
                        onError(llmError)
                    },
                    ifRight = { chunk ->
                        when (chunk) {
                            is LLMStreamChunk.ContentChunk -> {
                                accumulatedContent += chunk.deltaContent
                                onContentDelta(chunk.deltaContent)
                            }

                            is LLMStreamChunk.UsageChunk -> {
                                // Handle usage stats if needed
                            }

                            LLMStreamChunk.Done -> {
                                onStreamComplete(accumulatedContent)
                            }

                            is LLMStreamChunk.Error -> {
                                logger.error("LLM API returned streaming error chunk: ${chunk.llmError}")
                                onError(chunk.llmError)
                            }
                        }
                    }
                )
            }
    }

    private suspend fun getLlmConfig(
        modelId: Long,
        settingsId: Long
    ): Either<ProcessNewMessageError, LlmConfig> = either {
        val model = llmModelService.getModelById(modelId).mapLeft { serviceError ->
            ProcessNewMessageError.ModelConfigurationError("Model with ID $modelId not found: ${serviceError::class.simpleName}")
        }.bind()

        val settings = modelSettingsService.getSettingsById(settingsId).mapLeft { serviceError ->
            ProcessNewMessageError.ModelConfigurationError("Settings with ID $settingsId not found: ${serviceError::class.simpleName}")
        }.bind()

        val provider = llmProviderService.getProviderById(model.providerId).mapLeft { serviceError ->
            ProcessNewMessageError.ModelConfigurationError("Provider not found for model ID $modelId (provider ID: ${model.providerId}): ${serviceError::class.simpleName}")
        }.bind()

        val apiKey = provider.apiKeyId?.let { keyId ->
            credentialManager.getCredential(keyId).mapLeft { credError ->
                ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for provider ID ${provider.id} (key alias: $keyId)")
            }.bind()
        }
        LlmConfig(model, settings, provider, apiKey)
    }
}


/**
 * Data class to hold LLM configuration components.
 */
private data class LlmConfig(
    val model: LLMModel,
    val settings: ModelSettings,
    val provider: LLMProvider,
    val apiKey: String?
)